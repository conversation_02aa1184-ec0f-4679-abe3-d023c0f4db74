<?php
/**
 * Example code to add custom fields to WordPress user admin interface
 * Add this to your theme's functions.php or create as a separate plugin
 */

// Add custom fields to user profile page
add_action('show_user_profile', 'add_sap_custom_user_fields');
add_action('edit_user_profile', 'add_sap_custom_user_fields');

function add_sap_custom_user_fields($user) {
    ?>
    <h3>SAP Customer Information</h3>
    <table class="form-table">
        <tr>
            <th><label for="customer_id">Customer ID</label></th>
            <td>
                <input type="text" name="_customer" id="customer_id"
                       value="<?php echo esc_attr(get_user_meta($user->ID, '_customer', true)); ?>"
                       placeholder="Enter Customer ID" class="regular-text" />
                <p class="description">SAP Customer ID</p>
            </td>
        </tr>
        <tr>
            <th><label for="company_code">Company Code</label></th>
            <td>
                <input type="text" name="_companycode" id="company_code"
                       value="<?php echo esc_attr(get_user_meta($user->ID, '_companycode', true)); ?>"
                       placeholder="Enter Company Code" class="regular-text" />
                <p class="description">SAP Company Code</p>
            </td>
        </tr>
        <tr>
            <th><label for="country_code">Country Code</label></th>
            <td>
                <input type="text" name="_country" id="country_code"
                       value="<?php echo esc_attr(get_user_meta($user->ID, '_country', true)); ?>"
                       placeholder="Enter Country Code" class="regular-text" />
                <p class="description">Country Code (e.g., US, CA, UK)</p>
            </td>
        </tr>
        <tr>
            <th><label for="price_group">Price Group</label></th>
            <td>
                <input type="text" name="_pricegroup" id="price_group"
                       value="<?php echo esc_attr(get_user_meta($user->ID, '_pricegroup', true)); ?>"
                       placeholder="Enter Price Group" class="regular-text" />
                <p class="description">SAP Price Group</p>
            </td>
        </tr>
        <tr>
            <th><label for="shiptos">Ship-To Locations</label></th>
            <td>
                <?php
                $shiptos = get_user_meta($user->ID, '_shiptos', true);
                $shiptos_text = '';
                if (is_array($shiptos)) {
                    $shiptos_text = implode(', ', $shiptos);
                } elseif (!empty($shiptos)) {
                    $shiptos_text = $shiptos;
                }
                ?>
                <textarea name="_shiptos" id="shiptos" rows="3" cols="50" class="regular-text"
                          placeholder="Enter Ship-To locations separated by commas"><?php echo esc_textarea($shiptos_text); ?></textarea>
                <p class="description">Ship-To location IDs separated by commas (e.g., 1128545, 1131356, 1131487)</p>
            </td>
        </tr>
    </table>

    <h3>Billing Address Information</h3>
    <table class="form-table">
        <tr>
            <th><label for="billing_company">Billing Company</label></th>
            <td>
                <input type="text" name="billing_company" id="billing_company"
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'billing_company', true)); ?>"
                       placeholder="Enter Company Name" class="regular-text" />
                <p class="description">Company name for billing</p>
            </td>
        </tr>
        <tr>
            <th><label for="billing_address_1">Billing Address 1</label></th>
            <td>
                <input type="text" name="billing_address_1" id="billing_address_1"
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'billing_address_1', true)); ?>"
                       placeholder="Enter Street Address" class="regular-text" />
                <p class="description">Street address, P.O. box, company name, c/o</p>
            </td>
        </tr>
        <tr>
            <th><label for="billing_address_2">Billing Address 2</label></th>
            <td>
                <input type="text" name="billing_address_2" id="billing_address_2"
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'billing_address_2', true)); ?>"
                       placeholder="Enter Apartment, suite, etc." class="regular-text" />
                <p class="description">Apartment, suite, unit, building, floor, etc.</p>
            </td>
        </tr>
        <tr>
            <th><label for="billing_city">Billing City</label></th>
            <td>
                <input type="text" name="billing_city" id="billing_city"
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'billing_city', true)); ?>"
                       placeholder="Enter City" class="regular-text" />
                <p class="description">City</p>
            </td>
        </tr>
        <tr>
            <th><label for="billing_postcode">Billing Postal Code</label></th>
            <td>
                <input type="text" name="billing_postcode" id="billing_postcode"
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'billing_postcode', true)); ?>"
                       placeholder="Enter Postal Code" class="regular-text" />
                <p class="description">ZIP / Postal code</p>
            </td>
        </tr>
        <tr>
            <th><label for="billing_country">Billing Country</label></th>
            <td>
                <input type="text" name="billing_country" id="billing_country"
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'billing_country', true)); ?>"
                       placeholder="Enter Country Code" class="regular-text" />
                <p class="description">Country code (e.g., US, CA, UK)</p>
            </td>
        </tr>
        <tr>
            <th><label for="billing_state">Billing State/Province</label></th>
            <td>
                <input type="text" name="billing_state" id="billing_state"
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'billing_state', true)); ?>"
                       placeholder="Enter State/Province" class="regular-text" />
                <p class="description">State, province, or region</p>
            </td>
        </tr>
    </table>
    <?php
}

// Save custom fields when user profile is updated
add_action('personal_options_update', 'save_sap_custom_user_fields');
add_action('edit_user_profile_update', 'save_sap_custom_user_fields');

function save_sap_custom_user_fields($user_id) {
    if (!current_user_can('edit_user', $user_id)) {
        return false;
    }

    // Save SAP basic fields
    $sap_fields = ['_customer', '_companycode', '_country', '_pricegroup'];
    foreach ($sap_fields as $field) {
        if (isset($_POST[$field])) {
            update_user_meta($user_id, $field, sanitize_text_field($_POST[$field]));
        }
    }

    // Save billing address fields
    $billing_fields = [
        'billing_company', 'billing_address_1', 'billing_address_2',
        'billing_city', 'billing_postcode', 'billing_country', 'billing_state'
    ];
    foreach ($billing_fields as $field) {
        if (isset($_POST[$field])) {
            update_user_meta($user_id, $field, sanitize_text_field($_POST[$field]));
        }
    }

    // Save shiptos (handle as array)
    if (isset($_POST['_shiptos'])) {
        $shiptos_input = sanitize_textarea_field($_POST['_shiptos']);
        if (!empty($shiptos_input)) {
            // Convert comma-separated string to array
            $shiptos_array = array_map('trim', explode(',', $shiptos_input));
            $shiptos_array = array_filter($shiptos_array); // Remove empty values
            update_user_meta($user_id, '_shiptos', $shiptos_array);
        } else {
            delete_user_meta($user_id, '_shiptos');
        }
    }
}

// Add custom columns to users list table
add_filter('manage_users_columns', 'add_sap_user_columns');
function add_sap_user_columns($columns) {
    $columns['sap_customer_id'] = 'Customer ID';
    $columns['sap_company'] = 'Company Code';
    $columns['sap_country'] = 'Country';
    $columns['sap_price_group'] = 'Price Group';
    $columns['sap_billing_company'] = 'Billing Company';
    $columns['sap_shiptos'] = 'Ship-To Locations';
    return $columns;
}

// Display custom column content
add_filter('manage_users_custom_column', 'show_sap_user_column_content', 10, 3);
function show_sap_user_column_content($value, $column_name, $user_id) {
    switch ($column_name) {
        case 'sap_customer_id':
            return get_user_meta($user_id, '_customer', true);
        case 'sap_company':
            return get_user_meta($user_id, '_companycode', true);
        case 'sap_country':
            return get_user_meta($user_id, '_country', true);
        case 'sap_price_group':
            return get_user_meta($user_id, '_pricegroup', true);
        case 'sap_billing_company':
            return get_user_meta($user_id, 'billing_company', true);
        case 'sap_shiptos':
            $shiptos = get_user_meta($user_id, '_shiptos', true);
            if (is_array($shiptos)) {
                return implode(', ', array_slice($shiptos, 0, 2)) . (count($shiptos) > 2 ? '...' : '');
            }
            return $shiptos;
    }
    return $value;
}

// Make custom columns sortable (optional)
add_filter('manage_users_sortable_columns', 'make_sap_user_columns_sortable');
function make_sap_user_columns_sortable($columns) {
    $columns['sap_customer_id'] = 'sap_customer_id';
    $columns['sap_company'] = 'sap_company';
    $columns['sap_country'] = 'sap_country';
    $columns['sap_price_group'] = 'sap_price_group';
    $columns['sap_billing_company'] = 'sap_billing_company';
    return $columns;
}
?>
