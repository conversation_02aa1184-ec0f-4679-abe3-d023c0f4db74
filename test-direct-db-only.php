<?php
/**
 * Test direct database operations only - no WordPress user functions
 */

// Load WordPress for database connection
if (!defined('ABSPATH')) {
    if (file_exists('./wp-config.php')) {
        require_once('./wp-config.php');
    } elseif (file_exists('../wp-config.php')) {
        require_once('../wp-config.php');
    } else {
        die('WordPress not found.');
    }
}

echo "<h1>Direct Database Test</h1>\n";

global $wpdb;

$test_email = 'db_test_' . time() . '@example.com';
$test_username = 'db_test_' . time();
$customer_id = 'CUST_' . time();

echo "<p>Testing with email: {$test_email}</p>\n";
echo "<p>Customer ID: {$customer_id}</p>\n";

// Test 1: Check if user exists
echo "<h2>1. Checking if user exists</h2>\n";
$existing = $wpdb->get_var($wpdb->prepare(
    "SELECT ID FROM {$wpdb->users} WHERE user_email = %s OR user_login = %s",
    $test_email, $test_username
));

if ($existing) {
    echo "<p>❌ User already exists with ID: {$existing}</p>\n";
    exit;
} else {
    echo "<p>✅ User does not exist, proceeding with creation</p>\n";
}

// Test 2: Insert user
echo "<h2>2. Inserting user directly</h2>\n";

$password_hash = wp_hash_password('test_password_123');

$user_data = [
    'user_login'          => $test_username,
    'user_pass'           => $password_hash,
    'user_nicename'       => sanitize_title($test_username),
    'user_email'          => $test_email,
    'user_registered'     => current_time('mysql'),
    'user_activation_key' => '',
    'user_status'         => 0,
    'display_name'        => $test_email
];

echo "<p>Attempting database insert...</p>\n";

$result = $wpdb->insert($wpdb->users, $user_data);

if ($result === false) {
    echo "<p>❌ Database insert failed: " . $wpdb->last_error . "</p>\n";
    exit;
} else {
    $user_id = $wpdb->insert_id;
    echo "<p>✅ User inserted successfully with ID: {$user_id}</p>\n";
}

// Test 3: Add user capabilities
echo "<h2>3. Adding user capabilities</h2>\n";

$cap_result = $wpdb->insert($wpdb->usermeta, [
    'user_id'    => $user_id,
    'meta_key'   => $wpdb->prefix . 'capabilities',
    'meta_value' => serialize(['customer' => true])
]);

$level_result = $wpdb->insert($wpdb->usermeta, [
    'user_id'    => $user_id,
    'meta_key'   => $wpdb->prefix . 'user_level',
    'meta_value' => '0'
]);

echo "<p>Capabilities result: " . ($cap_result ? '✅ Success' : '❌ Failed: ' . $wpdb->last_error) . "</p>\n";
echo "<p>User level result: " . ($level_result ? '✅ Success' : '❌ Failed: ' . $wpdb->last_error) . "</p>\n";

// Test 4: Add custom metadata
echo "<h2>4. Adding custom metadata</h2>\n";

$metadata = [
    'customer_id'  => $customer_id,
    'company_code' => 'TEST_COMP',
    'country_code' => 'US',
    'price_group'  => 'STANDARD',
    'billing_company' => 'Test Company Inc.',
    'billing_address_1' => '123 Test Street',
    'billing_city' => 'Test City',
    'billing_postcode' => '12345',
    'billing_country' => 'US',
    'billing_state' => 'CA'
];

foreach ($metadata as $meta_key => $meta_value) {
    $meta_result = $wpdb->insert($wpdb->usermeta, [
        'user_id'    => $user_id,
        'meta_key'   => $meta_key,
        'meta_value' => $meta_value
    ]);
    
    echo "<p>{$meta_key}: " . ($meta_result ? '✅ Success' : '❌ Failed: ' . $wpdb->last_error) . "</p>\n";
}

// Test 5: Verify data
echo "<h2>5. Verifying saved data</h2>\n";

$saved_user = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->users} WHERE ID = %d", $user_id
));

if ($saved_user) {
    echo "<p>✅ User data verified:</p>\n";
    echo "<p>Login: {$saved_user->user_login}</p>\n";
    echo "<p>Email: {$saved_user->user_email}</p>\n";
    echo "<p>Registered: {$saved_user->user_registered}</p>\n";
} else {
    echo "<p>❌ Could not retrieve user data</p>\n";
}

$saved_meta = $wpdb->get_results($wpdb->prepare(
    "SELECT meta_key, meta_value FROM {$wpdb->usermeta} WHERE user_id = %d", $user_id
));

echo "<p>✅ Metadata saved:</p>\n";
foreach ($saved_meta as $meta) {
    $display_value = strlen($meta->meta_value) > 50 ? 
        substr($meta->meta_value, 0, 50) . '...' : 
        $meta->meta_value;
    echo "<p>{$meta->meta_key}: {$display_value}</p>\n";
}

// Test 6: Clean up
echo "<h2>6. Cleanup</h2>\n";

$delete_meta = $wpdb->delete($wpdb->usermeta, ['user_id' => $user_id]);
$delete_user = $wpdb->delete($wpdb->users, ['ID' => $user_id]);

echo "<p>Metadata deleted: " . ($delete_meta !== false ? '✅ Success' : '❌ Failed') . "</p>\n";
echo "<p>User deleted: " . ($delete_user !== false ? '✅ Success' : '❌ Failed') . "</p>\n";

echo "<h2>Test Complete</h2>\n";
echo "<p>If all tests passed, direct database operations work fine.</p>\n";
echo "<p>The issue is specifically with wp_insert_user() function.</p>\n";
?>
