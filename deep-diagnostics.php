<?php
/**
 * Deep diagnostic script to identify the source of "Security Violated" error
 */

// Load WordPress
if (!function_exists('wp_insert_user')) {
    if (file_exists('./wp-config.php')) {
        require_once('./wp-config.php');
    } elseif (file_exists('../wp-config.php')) {
        require_once('../wp-config.php');
    } else {
        die('WordPress not found.');
    }
}

echo "<h1>Deep Security Diagnostics</h1>\n";

// Test 1: Check for wp_die hooks
echo "<h2>1. WordPress Die Hooks</h2>\n";
global $wp_filter;
if (isset($wp_filter['wp_die_handler'])) {
    echo "<p>⚠️ wp_die_handler hooks found:</p>\n";
    foreach ($wp_filter['wp_die_handler']->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            echo "<p>Priority {$priority}: " . print_r($callback, true) . "</p>\n";
        }
    }
} else {
    echo "<p>✅ No wp_die_handler hooks found</p>\n";
}

// Test 2: Check for user creation hooks
echo "<h2>2. User Creation Hooks</h2>\n";
$user_hooks = ['user_register', 'wp_insert_user', 'wp_insert_user_data'];
foreach ($user_hooks as $hook) {
    if (isset($wp_filter[$hook])) {
        echo "<p>⚠️ {$hook} hooks found:</p>\n";
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                $func_name = is_array($callback['function']) ? 
                    (is_object($callback['function'][0]) ? get_class($callback['function'][0]) : $callback['function'][0]) . '::' . $callback['function'][1] :
                    $callback['function'];
                echo "<p>Priority {$priority}: {$func_name}</p>\n";
            }
        }
    } else {
        echo "<p>✅ No {$hook} hooks found</p>\n";
    }
}

// Test 3: Check active plugins
echo "<h2>3. Active Plugins</h2>\n";
$active_plugins = get_option('active_plugins', []);
if (empty($active_plugins)) {
    echo "<p>✅ No active plugins</p>\n";
} else {
    foreach ($active_plugins as $plugin) {
        echo "<p>📦 {$plugin}</p>\n";
    }
}

// Test 4: Check theme functions
echo "<h2>4. Theme Information</h2>\n";
$theme = wp_get_theme();
echo "<p>Theme: {$theme->get('Name')} v{$theme->get('Version')}</p>\n";
echo "<p>Theme Directory: {$theme->get_stylesheet_directory()}</p>\n";

// Test 5: Check for custom wp_die function
echo "<h2>5. Function Override Check</h2>\n";
$reflection = new ReflectionFunction('wp_die');
echo "<p>wp_die defined in: {$reflection->getFileName()}:{$reflection->getStartLine()}</p>\n";

// Test 6: Try to capture the exact error
echo "<h2>6. Detailed User Creation Test</h2>\n";

// Set up error handling
set_error_handler(function($severity, $message, $file, $line) {
    echo "<p>❌ PHP Error: {$message} in {$file}:{$line}</p>\n";
});

// Set up exception handling
set_exception_handler(function($exception) {
    echo "<p>❌ Exception: " . $exception->getMessage() . "</p>\n";
    echo "<p>File: " . $exception->getFile() . ":" . $exception->getLine() . "</p>\n";
    echo "<p>Trace: " . $exception->getTraceAsString() . "</p>\n";
});

// Capture output
ob_start();

$test_email = 'deep_test_' . time() . '@example.com';
$test_username = 'deep_test_' . time();

echo "<p>Testing with: {$test_email}</p>\n";

try {
    // Test step by step
    echo "<p>Step 1: Checking if user exists...</p>\n";
    $exists = username_exists($test_username) || email_exists($test_email);
    echo "<p>User exists: " . ($exists ? 'Yes' : 'No') . "</p>\n";
    
    if (!$exists) {
        echo "<p>Step 2: Generating password...</p>\n";
        $password = wp_generate_password(12, false);
        echo "<p>Password generated: " . strlen($password) . " characters</p>\n";
        
        echo "<p>Step 3: Preparing user data...</p>\n";
        $user_data = [
            'user_login' => $test_username,
            'user_pass'  => $password,
            'user_email' => $test_email,
            'role'       => 'customer',
        ];
        echo "<p>User data prepared</p>\n";
        
        echo "<p>Step 4: Calling wp_insert_user...</p>\n";
        $user_id = wp_insert_user($user_data);
        
        echo "<p>Step 5: Checking result...</p>\n";
        if (is_wp_error($user_id)) {
            echo "<p>❌ wp_insert_user returned WP_Error:</p>\n";
            echo "<p>Error Code: " . $user_id->get_error_code() . "</p>\n";
            echo "<p>Error Message: " . $user_id->get_error_message() . "</p>\n";
            echo "<p>Error Data: " . print_r($user_id->get_error_data(), true) . "</p>\n";
        } else {
            echo "<p>✅ User created with ID: {$user_id}</p>\n";
            
            // Clean up
            wp_delete_user($user_id);
            echo "<p>🧹 Test user deleted</p>\n";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ Exception caught: " . $e->getMessage() . "</p>\n";
} catch (Error $e) {
    echo "<p>❌ Fatal error caught: " . $e->getMessage() . "</p>\n";
}

$output = ob_get_clean();
echo $output;

// Test 7: Check WordPress constants
echo "<h2>7. WordPress Security Constants</h2>\n";
$security_constants = [
    'DISALLOW_FILE_EDIT', 'DISALLOW_FILE_MODS', 'FORCE_SSL_ADMIN',
    'WP_DEBUG', 'WP_DEBUG_LOG', 'SCRIPT_DEBUG',
    'AUTOMATIC_UPDATER_DISABLED', 'WP_AUTO_UPDATE_CORE'
];

foreach ($security_constants as $constant) {
    if (defined($constant)) {
        $value = constant($constant);
        echo "<p>{$constant}: " . var_export($value, true) . "</p>\n";
    } else {
        echo "<p>{$constant}: Not defined</p>\n";
    }
}

// Test 8: Server environment
echo "<h2>8. Server Environment</h2>\n";
echo "<p>PHP Version: " . PHP_VERSION . "</p>\n";
echo "<p>Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>\n";
echo "<p>User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "</p>\n";
echo "<p>Request Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'Unknown') . "</p>\n";

echo "<h2>Diagnosis Complete</h2>\n";
?>
