<?php
/**
 * <PERSON>ript to inspect user metadata structure in your WordPress installation
 */

// Load WordPress
if (!function_exists('get_user_meta')) {
    if (file_exists('./wp-config.php')) {
        require_once('./wp-config.php');
    } elseif (file_exists('../wp-config.php')) {
        require_once('../wp-config.php');
    } else {
        die('WordPress not found.');
    }
}

echo "<h1>User Metadata Inspection</h1>\n";

// Get a sample of users to see what metadata keys are being used
$users = get_users([
    'number' => 10,
    'orderby' => 'registered',
    'order' => 'DESC'
]);

if (empty($users)) {
    echo "<p>❌ No users found</p>\n";
    exit;
}

echo "<h2>Sample User Metadata Keys</h2>\n";

$all_meta_keys = [];

foreach ($users as $user) {
    $user_meta = get_user_meta($user->ID);
    
    echo "<h3>User: {$user->user_login} (ID: {$user->ID})</h3>\n";
    echo "<p>Roles: " . implode(', ', $user->roles) . "</p>\n";
    
    if (!empty($user_meta)) {
        echo "<h4>Metadata Keys:</h4>\n";
        echo "<ul>\n";
        
        foreach ($user_meta as $key => $values) {
            $all_meta_keys[$key] = ($all_meta_keys[$key] ?? 0) + 1;
            $value = is_array($values) ? $values[0] : $values;
            $display_value = strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value;
            
            // Highlight potential custom fields
            $is_custom = false;
            $custom_indicators = ['customer', 'company', 'country', 'price', 'billing', 'shipping'];
            foreach ($custom_indicators as $indicator) {
                if (stripos($key, $indicator) !== false) {
                    $is_custom = true;
                    break;
                }
            }
            
            $style = $is_custom ? 'style="background-color: yellow; font-weight: bold;"' : '';
            echo "<li {$style}>{$key}: {$display_value}</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<p>No metadata found</p>\n";
    }
    
    echo "<hr>\n";
}

echo "<h2>All Metadata Keys Summary</h2>\n";
echo "<p>Keys that appear across users (with frequency):</p>\n";

// Sort by frequency
arsort($all_meta_keys);

echo "<ul>\n";
foreach ($all_meta_keys as $key => $count) {
    // Highlight potential custom fields
    $is_custom = false;
    $custom_indicators = ['customer', 'company', 'country', 'price', 'billing', 'shipping'];
    foreach ($custom_indicators as $indicator) {
        if (stripos($key, $indicator) !== false) {
            $is_custom = true;
            break;
        }
    }
    
    $style = $is_custom ? 'style="background-color: yellow; font-weight: bold;"' : '';
    echo "<li {$style}>{$key} (used by {$count} users)</li>\n";
}
echo "</ul>\n";

echo "<h2>Looking for Custom Field Patterns</h2>\n";
echo "<p>Keys that might be related to your custom fields:</p>\n";

$target_fields = ['customer', 'company', 'country', 'price'];
foreach ($target_fields as $target) {
    echo "<h3>Keys containing '{$target}':</h3>\n";
    echo "<ul>\n";
    
    $found = false;
    foreach ($all_meta_keys as $key => $count) {
        if (stripos($key, $target) !== false) {
            echo "<li><strong>{$key}</strong> (used by {$count} users)</li>\n";
            $found = true;
        }
    }
    
    if (!$found) {
        echo "<li>No keys found containing '{$target}'</li>\n";
    }
    
    echo "</ul>\n";
}

// Check database directly for any custom field patterns
global $wpdb;
echo "<h2>Database Query for Custom Field Patterns</h2>\n";

$custom_meta_query = "
    SELECT DISTINCT meta_key, COUNT(*) as usage_count 
    FROM {$wpdb->usermeta} 
    WHERE meta_key LIKE '%customer%' 
       OR meta_key LIKE '%company%' 
       OR meta_key LIKE '%country%' 
       OR meta_key LIKE '%price%'
    GROUP BY meta_key 
    ORDER BY usage_count DESC
";

$custom_meta_results = $wpdb->get_results($custom_meta_query);

if (!empty($custom_meta_results)) {
    echo "<p>Custom field patterns found in database:</p>\n";
    echo "<ul>\n";
    foreach ($custom_meta_results as $result) {
        echo "<li><strong>{$result->meta_key}</strong> (used {$result->usage_count} times)</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "<p>No custom field patterns found in database.</p>\n";
}

echo "<h2>Recommendations</h2>\n";
echo "<p>Based on this analysis, try using the highlighted metadata keys for your custom fields.</p>\n";
echo "<p>Look for patterns like:</p>\n";
echo "<ul>\n";
echo "<li>_customer_id or customer_id</li>\n";
echo "<li>_company_code or company_code</li>\n";
echo "<li>billing_* fields</li>\n";
echo "<li>Any keys with underscores prefix (_)</li>\n";
echo "</ul>\n";
?>
