<!DOCTYPE html>
<html>
<head>
    <title>SAP SoldTo API Tester</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        textarea { width: 100%; height: 200px; font-family: monospace; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>SAP SoldTo API Endpoint Tester</h1>
        
        <div class="test-section">
            <h2>Configuration</h2>
            <label>WordPress Site URL:</label>
            <input type="text" id="siteUrl" value="http://localhost" style="width: 300px;" placeholder="http://your-wordpress-site.com">
            <br><br>
            <label>Username:</label>
            <input type="text" id="username" value="admin" style="width: 200px;">
            <label>Password:</label>
            <input type="password" id="password" value="" style="width: 200px;">
        </div>

        <div class="test-section">
            <h2>Test Data</h2>
            <textarea id="testData">{
  "soldTo": {
    "customerId": "TEST_CUSTOMER_123",
    "email": "<EMAIL>",
    "companyCode": "COMP01",
    "countryCode": "US",
    "priceGroup": "STANDARD"
  },
  "billingAddress": {
    "company": "Test Company Inc.",
    "address": {
      "line1": "123 Test Street",
      "line2": "Suite 456",
      "city": "Test City",
      "postcode": "12345",
      "countryRegion": "US",
      "stateCounty": "CA"
    }
  },
  "shiptos": ["SHIPTO001", "SHIPTO002"]
}</textarea>
            <br>
            <button onclick="generateNewTestData()">Generate New Test Data</button>
        </div>

        <div class="test-section">
            <h2>Test Endpoints</h2>
            <button onclick="testOriginalEndpoint()">Test Original Endpoint (/sap-soldto)</button>
            <button onclick="testDebugEndpoint()">Test Debug Endpoint (/sap-soldto-debug)</button>
            <button onclick="testBothEndpoints()">Test Both Endpoints</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function generateNewTestData() {
            const timestamp = Date.now();
            const testData = {
                soldTo: {
                    customerId: `TEST_CUSTOMER_${timestamp}`,
                    email: `test${timestamp}@example.com`,
                    companyCode: "COMP01",
                    countryCode: "US",
                    priceGroup: "STANDARD"
                },
                billingAddress: {
                    company: "Test Company Inc.",
                    address: {
                        line1: "123 Test Street",
                        line2: "Suite 456",
                        city: "Test City",
                        postcode: "12345",
                        countryRegion: "US",
                        stateCounty: "CA"
                    }
                },
                shiptos: ["SHIPTO001", "SHIPTO002"]
            };
            document.getElementById('testData').value = JSON.stringify(testData, null, 2);
        }

        async function testEndpoint(endpointPath, endpointName) {
            const siteUrl = document.getElementById('siteUrl').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const testData = document.getElementById('testData').value;

            const url = `${siteUrl}/wp-json/wc/v3${endpointPath}`;
            
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (username && password) {
                headers['Authorization'] = 'Basic ' + btoa(username + ':' + password);
            }

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: headers,
                    body: testData
                });

                const result = await response.json();
                
                displayResult(endpointName, response.status, result, url);
                
            } catch (error) {
                displayResult(endpointName, 'ERROR', { error: error.message }, url);
            }
        }

        function displayResult(endpointName, status, result, url) {
            const resultsDiv = document.getElementById('results');
            const isSuccess = status >= 200 && status < 300;
            
            const resultHtml = `
                <div class="test-section ${isSuccess ? 'success' : 'error'}">
                    <h3>${endpointName} - Status: ${status}</h3>
                    <p><strong>URL:</strong> ${url}</p>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                </div>
            `;
            
            resultsDiv.innerHTML += resultHtml;
        }

        function testOriginalEndpoint() {
            document.getElementById('results').innerHTML = '';
            testEndpoint('/sap-soldto', 'Original Endpoint');
        }

        function testDebugEndpoint() {
            document.getElementById('results').innerHTML = '';
            testEndpoint('/sap-soldto-debug', 'Debug Endpoint (No Auth)');
        }

        function testBothEndpoints() {
            document.getElementById('results').innerHTML = '';
            testEndpoint('/sap-soldto', 'Original Endpoint');
            setTimeout(() => {
                testEndpoint('/sap-soldto-debug', 'Debug Endpoint (No Auth)');
            }, 1000);
        }

        // Generate initial test data
        generateNewTestData();
    </script>
</body>
</html>
