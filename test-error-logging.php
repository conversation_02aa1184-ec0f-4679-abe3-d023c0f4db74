<?php
/**
 * Test script to verify SAP SoldTo error logging functionality
 * This script tests various error conditions to ensure they are properly logged
 */

// Test 1: Unauthenticated request (should trigger rest_not_logged_in error)
echo "=== Test 1: Unauthenticated Request ===\n";
$response1 = wp_remote_post( home_url( '/wp-json/wc/v3/sap-soldto' ), [
    'headers' => [
        'Content-Type' => 'application/json',
    ],
    'body' => wp_json_encode([
        'soldTo' => [
            'customerId' => 'TEST123',
            'email' => '<EMAIL>'
        ]
    ])
]);

if ( is_wp_error( $response1 ) ) {
    echo "Request failed: " . $response1->get_error_message() . "\n";
} else {
    $body1 = wp_remote_retrieve_body( $response1 );
    $status1 = wp_remote_retrieve_response_code( $response1 );
    echo "Status: {$status1}\n";
    echo "Response: {$body1}\n";
}

echo "\n";

// Test 2: Invalid JSON data (should trigger invalid_data error)
echo "=== Test 2: Invalid JSON Data ===\n";
$response2 = wp_remote_post( home_url( '/wp-json/wc/v3/sap-soldto' ), [
    'headers' => [
        'Content-Type' => 'application/json',
        'Authorization' => 'Basic ' . base64_encode( 'admin:password' ) // Replace with valid credentials
    ],
    'body' => 'invalid json data'
]);

if ( is_wp_error( $response2 ) ) {
    echo "Request failed: " . $response2->get_error_message() . "\n";
} else {
    $body2 = wp_remote_retrieve_body( $response2 );
    $status2 = wp_remote_retrieve_response_code( $response2 );
    echo "Status: {$status2}\n";
    echo "Response: {$body2}\n";
}

echo "\n";

// Test 3: Missing soldTo data (should trigger missing_soldto error)
echo "=== Test 3: Missing soldTo Data ===\n";
$response3 = wp_remote_post( home_url( '/wp-json/wc/v3/sap-soldto' ), [
    'headers' => [
        'Content-Type' => 'application/json',
        'Authorization' => 'Basic ' . base64_encode( 'admin:password' ) // Replace with valid credentials
    ],
    'body' => wp_json_encode([
        'someOtherData' => 'test'
    ])
]);

if ( is_wp_error( $response3 ) ) {
    echo "Request failed: " . $response3->get_error_message() . "\n";
} else {
    $body3 = wp_remote_retrieve_body( $response3 );
    $status3 = wp_remote_retrieve_response_code( $response3 );
    echo "Status: {$status3}\n";
    echo "Response: {$body3}\n";
}

echo "\n";

// Test 4: Missing customerId (should trigger missing_customer_id error)
echo "=== Test 4: Missing customerId ===\n";
$response4 = wp_remote_post( home_url( '/wp-json/wc/v3/sap-soldto' ), [
    'headers' => [
        'Content-Type' => 'application/json',
        'Authorization' => 'Basic ' . base64_encode( 'admin:password' ) // Replace with valid credentials
    ],
    'body' => wp_json_encode([
        'soldTo' => [
            'email' => '<EMAIL>'
            // customerId is missing
        ]
    ])
]);

if ( is_wp_error( $response4 ) ) {
    echo "Request failed: " . $response4->get_error_message() . "\n";
} else {
    $body4 = wp_remote_retrieve_body( $response4 );
    $status4 = wp_remote_retrieve_response_code( $response4 );
    echo "Status: {$status4}\n";
    echo "Response: {$body4}\n";
}

echo "\n";

// Test 5: Invalid email format (should trigger invalid_email error)
echo "=== Test 5: Invalid Email Format ===\n";
$response5 = wp_remote_post( home_url( '/wp-json/wc/v3/sap-soldto' ), [
    'headers' => [
        'Content-Type' => 'application/json',
        'Authorization' => 'Basic ' . base64_encode( 'admin:password' ) // Replace with valid credentials
    ],
    'body' => wp_json_encode([
        'soldTo' => [
            'customerId' => 'TEST123',
            'email' => 'invalid-email-format'
        ]
    ])
]);

if ( is_wp_error( $response5 ) ) {
    echo "Request failed: " . $response5->get_error_message() . "\n";
} else {
    $body5 = wp_remote_retrieve_body( $response5 );
    $status5 = wp_remote_retrieve_response_code( $response5 );
    echo "Status: {$status5}\n";
    echo "Response: {$body5}\n";
}

echo "\n=== Test Complete ===\n";
echo "Check the APIlogs-SoldTo.log file in wp-content/logs/ for detailed error logging.\n";
echo "Log file location: " . WP_CONTENT_DIR . "/logs/APIlogs-SoldTo.log\n";

// Display recent log entries
$log_file = WP_CONTENT_DIR . '/logs/APIlogs-SoldTo.log';
if ( file_exists( $log_file ) ) {
    echo "\n=== Recent Log Entries ===\n";
    $log_content = file_get_contents( $log_file );
    $log_lines = explode( "\n", $log_content );
    $recent_lines = array_slice( $log_lines, -20 ); // Show last 20 lines
    echo implode( "\n", $recent_lines );
} else {
    echo "\nLog file not found at: {$log_file}\n";
}
?>
