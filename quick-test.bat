@echo off
setlocal enabledelayedexpansion

REM Quick test script for SAP SoldTo API (Windows)
REM Usage: quick-test.bat [your-site-url]

set SITE_URL=%1
if "%SITE_URL%"=="" set SITE_URL=http://localhost

echo Quick SAP SoldTo API Test
echo =========================
echo Site: %SITE_URL%
echo.

echo 1. Testing if endpoint is registered (GET test endpoint):
echo --------------------------------------------------------
curl -s "%SITE_URL%/wp-json/wc/v3/sap-soldto-test"

echo.
echo.

echo 2. Testing main endpoint with minimal data:
echo -------------------------------------------

REM Generate timestamp (simplified for Windows)
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /format:list') do set datetime=%%I
set TIMESTAMP=%datetime:~0,14%

set TEST_DATA={"soldTo":{"customerId":"TEST_%TIMESTAMP%","email":"<EMAIL>"}}

echo Sending: %TEST_DATA%
echo.

curl -X POST -H "Content-Type: application/json" -d "%TEST_DATA%" "%SITE_URL%/wp-json/wc/v3/sap-soldto"

echo.
echo.
echo Check your WordPress error logs for messages starting with 🚀 or 🔍
echo If you see "SAP-SoldTo API ENDPOINT CALLED!" then the endpoint is working
echo Look for user creation and metadata saving logs

pause
