# Quick test script for SAP SoldTo API (PowerShell)
# Usage: .\quick-test.ps1 [your-site-url]

param(
    [string]$SiteUrl = "http://localhost"
)

Write-Host "Quick SAP SoldTo API Test" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host "Site: $SiteUrl"
Write-Host ""

Write-Host "1. Testing if endpoint is registered (GET test endpoint):" -ForegroundColor Yellow
Write-Host "--------------------------------------------------------" -ForegroundColor Yellow

try {
    $testResponse = Invoke-RestMethod -Uri "$SiteUrl/wp-json/wc/v3/sap-soldto-test" -Method Get
    Write-Host "✅ Test endpoint response:" -ForegroundColor Green
    $testResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "❌ Test endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host ""

Write-Host "2. Testing main endpoint with minimal data:" -ForegroundColor Yellow
Write-Host "-------------------------------------------" -ForegroundColor Yellow

$timestamp = [DateTimeOffset]::Now.ToUnixTimeSeconds()
$testData = @{
    soldTo = @{
        customerId = "TEST_$timestamp"
        email = "test$<EMAIL>"
    }
} | ConvertTo-Json -Depth 3

Write-Host "Sending: $testData"
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri "$SiteUrl/wp-json/wc/v3/sap-soldto" -Method Post -Body $testData -ContentType "application/json"
    Write-Host "✅ SUCCESS: Endpoint is working!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 3
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    $errorBody = $_.Exception.Response | ConvertFrom-Json -ErrorAction SilentlyContinue
    
    Write-Host "❌ ERROR: HTTP Status $statusCode" -ForegroundColor Red
    
    if ($statusCode -eq 404) {
        Write-Host "Endpoint not found. Make sure the plugin is activated." -ForegroundColor Red
    } elseif ($statusCode -eq 500) {
        Write-Host "Server error. Check WordPress error logs." -ForegroundColor Red
    }
    
    if ($errorBody) {
        Write-Host "Error details:" -ForegroundColor Red
        $errorBody | ConvertTo-Json -Depth 3
    } else {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "- Check WordPress error logs for messages starting with 🚀 or 🔍" -ForegroundColor Cyan
Write-Host "- If you see 'SAP-SoldTo API ENDPOINT CALLED!' then the endpoint is working" -ForegroundColor Cyan
Write-Host "- Look for user creation and metadata saving logs" -ForegroundColor Cyan
