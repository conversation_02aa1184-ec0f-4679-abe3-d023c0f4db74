<?php
/**
 * Diagnostic script to test user creation capabilities
 * Place this in your WordPress root and access via browser or WP-CLI
 */

// Simple WordPress detection
if (!function_exists('wp_insert_user')) {
    // Try to load WordPress
    if (file_exists('./wp-config.php')) {
        require_once('./wp-config.php');
    } elseif (file_exists('../wp-config.php')) {
        require_once('../wp-config.php');
    } else {
        die('WordPress not found. Place this file in your WordPress root directory.');
    }
}

echo "<h1>WordPress User Creation Diagnostics</h1>\n";

// Test 1: Basic WordPress functions
echo "<h2>1. WordPress Function Availability</h2>\n";
$functions = ['wp_insert_user', 'add_user_meta', 'update_user_meta', 'get_user_meta', 'wp_generate_password'];
foreach ($functions as $func) {
    $status = function_exists($func) ? '✅' : '❌';
    echo "<p>{$status} {$func}</p>\n";
}

// Test 2: WooCommerce availability
echo "<h2>2. WooCommerce Status</h2>\n";
$wc_active = class_exists('WooCommerce');
$wc_customer = class_exists('WC_Customer');
echo "<p>" . ($wc_active ? '✅' : '❌') . " WooCommerce Active</p>\n";
echo "<p>" . ($wc_customer ? '✅' : '❌') . " WC_Customer Class Available</p>\n";

// Test 3: Database connectivity
echo "<h2>3. Database Connectivity</h2>\n";
global $wpdb;
if ($wpdb) {
    $users_table = $wpdb->prefix . 'users';
    $usermeta_table = $wpdb->prefix . 'usermeta';
    
    $users_exists = $wpdb->get_var("SHOW TABLES LIKE '{$users_table}'") == $users_table;
    $usermeta_exists = $wpdb->get_var("SHOW TABLES LIKE '{$usermeta_table}'") == $usermeta_table;
    
    echo "<p>" . ($users_exists ? '✅' : '❌') . " Users table exists ({$users_table})</p>\n";
    echo "<p>" . ($usermeta_exists ? '✅' : '❌') . " User meta table exists ({$usermeta_table})</p>\n";
    
    // Test write permissions
    $test_query = $wpdb->get_var("SELECT COUNT(*) FROM {$users_table}");
    echo "<p>" . (is_numeric($test_query) ? '✅' : '❌') . " Database read access (found {$test_query} users)</p>\n";
} else {
    echo "<p>❌ WordPress database connection not available</p>\n";
}

// Test 4: User creation test
echo "<h2>4. User Creation Test</h2>\n";

$test_email = 'diagnostic_test_' . time() . '@example.com';
$test_username = 'diagnostic_test_' . time();

echo "<p>Testing with email: {$test_email}</p>\n";

// Check if user already exists
if (username_exists($test_username) || email_exists($test_email)) {
    echo "<p>⚠️ Test user already exists, skipping creation test</p>\n";
} else {
    $user_data = [
        'user_login' => $test_username,
        'user_pass'  => wp_generate_password(12, false),
        'user_email' => $test_email,
        'role'       => 'customer',
    ];
    
    echo "<p>Attempting to create user...</p>\n";
    $user_id = wp_insert_user($user_data);
    
    if (is_wp_error($user_id)) {
        echo "<p>❌ User creation failed: " . $user_id->get_error_message() . "</p>\n";
        echo "<p>Error data: " . print_r($user_id->get_error_data(), true) . "</p>\n";
    } else {
        echo "<p>✅ User created successfully with ID: {$user_id}</p>\n";
        
        // Test metadata
        $meta_result = add_user_meta($user_id, 'test_meta_key', 'test_meta_value');
        if ($meta_result) {
            echo "<p>✅ User metadata added successfully</p>\n";
            
            $retrieved_meta = get_user_meta($user_id, 'test_meta_key', true);
            if ($retrieved_meta === 'test_meta_value') {
                echo "<p>✅ User metadata retrieved successfully</p>\n";
            } else {
                echo "<p>❌ User metadata retrieval failed</p>\n";
            }
        } else {
            echo "<p>❌ User metadata addition failed</p>\n";
        }
        
        // Clean up test user
        wp_delete_user($user_id);
        echo "<p>🧹 Test user cleaned up</p>\n";
    }
}

// Test 5: Current user context
echo "<h2>5. Current User Context</h2>\n";
$current_user = wp_get_current_user();
echo "<p>Current User ID: " . $current_user->ID . "</p>\n";
echo "<p>Current User Login: " . $current_user->user_login . "</p>\n";
echo "<p>Current User Roles: " . implode(', ', $current_user->roles) . "</p>\n";

// Test 6: WordPress environment
echo "<h2>6. WordPress Environment</h2>\n";
echo "<p>WordPress Version: " . get_bloginfo('version') . "</p>\n";
echo "<p>PHP Version: " . PHP_VERSION . "</p>\n";
echo "<p>WP_DEBUG: " . (defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled') . "</p>\n";
echo "<p>WP_DEBUG_LOG: " . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'Enabled' : 'Disabled') . "</p>\n";

echo "<h2>Diagnosis Complete</h2>\n";
echo "<p>If all tests pass, the issue might be in the specific endpoint logic or plugin conflicts.</p>\n";
?>
