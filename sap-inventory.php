<?php
/**
 * Plugin Name: SAP Inventory API
 * Description: Processes SAP inventory data and updates WooCommerce product stock levels
 * Version: 1.0
 * Author: ATAK Interactive
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-inventory', [
        'methods'             => 'POST',
        'callback'            => 'sap_process_inventory',
        'permission_callback' => 'sap_inventory_permission_check',
    ] );
    
    // Test endpoint
    register_rest_route( 'wc/v3', '/sap-inventory-test', [
        'methods'             => 'GET',
        'callback'            => function() {
            return rest_ensure_response([
                'status' => 'working',
                'message' => 'SAP Inventory API is active',
                'timestamp' => current_time( 'mysql' ),
                'auth_required' => 'yes'
            ]);
        },
        'permission_callback' => '__return_true',
    ] );

    // Debug endpoint to inspect product meta
    register_rest_route( 'wc/v3', '/sap-inventory-debug', [
        'methods'             => 'POST',
        'callback'            => function( WP_REST_Request $request ) {
            $data = $request->get_json_params();
            if ( empty( $data['product_id'] ) ) {
                return new WP_Error( 'missing_product_id', 'product_id is required', [ 'status' => 400 ] );
            }

            $product_id = (int) $data['product_id'];
            $stock_levels = get_product_stock_levels( $product_id );

            // Get all meta to see what's actually stored
            $all_meta = get_post_meta( $product_id );
            $filtered_meta = [];
            foreach ( $all_meta as $key => $value ) {
                $filtered_meta[$key] = is_array( $value ) && count( $value ) === 1 ? $value[0] : $value;
            }

            return rest_ensure_response([
                'product_id' => $product_id,
                'current_stock_levels' => $stock_levels,
                'all_meta' => $filtered_meta,
                'product_title' => get_the_title( $product_id ),
                'product_type' => get_post_type( $product_id )
            ]);
        },
        'permission_callback' => '__return_true',
    ] );
} );

/**
 * Permission callback for SAP Inventory API
 * Uses WordPress REST API authentication (Basic Auth or Application Passwords)
 */
function sap_inventory_permission_check( WP_REST_Request $request ) {
    error_log( "🔍 SAP Inventory API: Checking WordPress REST API authentication" );
    
    // Get the current user (WordPress handles authentication automatically)
    $user = wp_get_current_user();
    
    if ( ! $user || ! $user->ID ) {
        error_log( "❌ SAP Inventory API: No authenticated user found" );
        return new WP_Error( 'rest_not_logged_in', 'You are not currently logged in.', [ 'status' => 401 ] );
    }
    
    error_log( "🔍 SAP Inventory API: Authenticated user: {$user->ID} ({$user->user_login})" );
    
    // Check if user has appropriate capabilities
    $required_capabilities = [
        'manage_options',       // Administrator capability
        'manage_woocommerce',   // WooCommerce admin
        'edit_shop_orders',     // WooCommerce orders
        'edit_users',           // User management
        'edit_products',        // Product management
    ];

    foreach ( $required_capabilities as $cap ) {
        if ( user_can( $user, $cap ) ) {
            error_log( "✅ SAP Inventory API: User has capability: {$cap}" );
            return true;
        }
    }

    // Check for specific roles
    $user_roles = $user->roles ?? [];
    $allowed_roles = [ 'administrator', 'shop_manager', 'editor' ];
    
    foreach ( $allowed_roles as $role ) {
        if ( in_array( $role, $user_roles ) ) {
            error_log( "✅ SAP Inventory API: User has allowed role: {$role}" );
            return true;
        }
    }

    error_log( "❌ SAP Inventory API: User lacks sufficient permissions. Roles: " . implode( ', ', $user_roles ) );
    return new WP_Error( 'rest_forbidden', 'Sorry, you are not allowed to access this endpoint.', [ 'status' => 403 ] );
}

/**
 * Main endpoint to process SAP inventory data
 */
function sap_process_inventory( WP_REST_Request $request ) {
    $start_time = microtime( true );
    $request_id = uniqid( 'sap_inventory_' );

    // Log API call start with comprehensive details
    sap_inventory_log_api( "🚀 SAP-Inventory API CALL START [ID: {$request_id}]" );
    sap_inventory_log_api( "📡 Request Method: " . $request->get_method() );
    sap_inventory_log_api( "📡 Request URL: " . $request->get_route() );
    sap_inventory_log_api( "📡 Request Time: " . current_time( 'mysql' ) );
    sap_inventory_log_api( "📡 User Agent: " . ( $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown' ) );
    sap_inventory_log_api( "📡 Client IP: " . sap_inventory_get_client_ip() );

    // Log authentication details
    $current_user = wp_get_current_user();
    if ( $current_user && $current_user->ID ) {
        sap_inventory_log_api( "🔐 Authenticated User: {$current_user->ID} ({$current_user->user_login})" );
    }

    $data = $request->get_json_params();

    // Log the incoming data (sanitized for security)
    $sanitized_data = sap_inventory_sanitize_log_data( $data );
    sap_inventory_log_api( "📥 SAP-Inventory Request Data [ID: {$request_id}]: " . wp_json_encode( $sanitized_data, JSON_PRETTY_PRINT ) );

    // Validation
    if ( empty( $data ) || ! is_array( $data ) || empty( $data['inventory'] ) ) {
        error_log( "❌ Invalid data structure" );
        return new WP_Error( 'invalid_data', 'Invalid data structure. Expected inventory object.', [ 'status' => 400 ] );
    }

    $inventory = $data['inventory'];
    
    // Validate required fields
    if ( empty( $inventory['materialNumber'] ) ) {
        error_log( "❌ Missing materialNumber" );
        return new WP_Error( 'missing_material_number', 'materialNumber is required', [ 'status' => 400 ] );
    }

    if ( empty( $inventory['stock'] ) || ! is_array( $inventory['stock'] ) ) {
        error_log( "❌ Missing or invalid stock data" );
        return new WP_Error( 'missing_stock_data', 'stock data is required and must be an object', [ 'status' => 400 ] );
    }

    $material_number = sanitize_text_field( $inventory['materialNumber'] );
    
    // Find product by material number (title)
    $product_id = find_product_by_material_number( $material_number );
    
    if ( ! $product_id ) {
        error_log( "❌ Product not found for material number: {$material_number}" );
        return new WP_Error( 'product_not_found', "Product not found for materialNumber: {$material_number}", [ 'status' => 404 ] );
    }

    error_log( "🔍 Found product ID {$product_id} for material number {$material_number}" );

    // Process stock data for each region
    $updated_regions = [];
    $errors = [];

    foreach ( $inventory['stock'] as $region => $stock_data ) {
        $result = update_product_stock_for_region( $product_id, $region, $stock_data );
        
        if ( is_wp_error( $result ) ) {
            $errors[] = "Region {$region}: " . $result->get_error_message();
            error_log( "❌ Error updating stock for region {$region}: " . $result->get_error_message() );
        } else {
            $updated_regions[] = $result;
            error_log( "✅ Successfully updated stock for region {$region}" );
        }
    }

    // Return response
    $response = [
        'success' => true,
        'product_id' => $product_id,
        'material_number' => $material_number,
        'updated_regions' => $updated_regions,
        'total_regions_updated' => count( $updated_regions ),
        'errors' => $errors
    ];

    if ( ! empty( $errors ) ) {
        $response['partial_success'] = true;
    }

    // Log API call completion
    $end_time = microtime( true );
    $execution_time = round( ( $end_time - $start_time ) * 1000, 2 ); // Convert to milliseconds

    sap_inventory_log_api( "📤 SAP-Inventory Response [ID: {$request_id}]: " . wp_json_encode( $response, JSON_PRETTY_PRINT ) );
    sap_inventory_log_api( "⏱️ SAP-Inventory API CALL COMPLETE [ID: {$request_id}] - Execution Time: {$execution_time}ms" );
    sap_inventory_log_api( "✅ SAP-Inventory Success: {$response['success']}, Product: {$response['material_number']}, Regions Updated: {$response['total_regions_updated']}" );

    return rest_ensure_response( $response );
}

/**
 * Find WooCommerce product by material number (title)
 */
function find_product_by_material_number( $material_number ) {
    // Search for product by title
    $products = get_posts([
        'post_type' => 'product',
        'post_status' => 'publish',
        'title' => $material_number,
        'numberposts' => 1,
        'fields' => 'ids'
    ]);

    if ( ! empty( $products ) ) {
        return $products[0];
    }

    // Fallback: search by title using WP_Query for exact match
    $query = new WP_Query([
        'post_type' => 'product',
        'post_status' => 'publish',
        'posts_per_page' => 1,
        'fields' => 'ids',
        'meta_query' => [
            'relation' => 'OR',
            [
                'key' => '_sku',
                'value' => $material_number,
                'compare' => '='
            ]
        ]
    ]);

    if ( $query->have_posts() ) {
        return $query->posts[0];
    }

    // Last resort: search by post title with LIKE
    global $wpdb;
    $product_id = $wpdb->get_var( $wpdb->prepare(
        "SELECT ID FROM {$wpdb->posts} WHERE post_type = 'product' AND post_status = 'publish' AND post_title = %s LIMIT 1",
        $material_number
    ) );

    return $product_id ? (int) $product_id : false;
}

/**
 * Update product stock for a specific region
 */
function update_product_stock_for_region( $product_id, $region, $stock_data ) {
    // Validate stock data
    if ( ! isset( $stock_data['quantity'] ) ) {
        return new WP_Error( 'missing_quantity', 'quantity is required for stock data' );
    }

    $quantity = (int) $stock_data['quantity'];
    $allow_backorders = isset( $stock_data['allowBackorders'] ) ? $stock_data['allowBackorders'] : false;

    // Convert boolean to yes/no for WooCommerce
    $backorders_value = $allow_backorders ? 'yes' : 'no';

    // Generate meta keys based on region
    $region_lower = strtolower( sanitize_key( $region ) );
    $stock_meta_key = "_stock_{$region_lower}";
    $backorders_meta_key = "_backorders_{$region_lower}";

    error_log( "🔍 Updating product {$product_id} - Region: {$region}, Stock: {$quantity}, Backorders: {$backorders_value}" );

    // Update stock quantity for region
    $stock_result = update_post_meta( $product_id, $stock_meta_key, $quantity );
    if ( $stock_result === false ) {
        error_log( "❌ Failed to update stock meta {$stock_meta_key} for product {$product_id}" );
        return new WP_Error( 'stock_update_failed', "Failed to update stock for region {$region}" );
    }
    error_log( "✅ Updated stock meta {$stock_meta_key} = {$quantity}" );

    // Update backorders setting for region with improved error handling
    $existing_backorders = get_post_meta( $product_id, $backorders_meta_key, true );
    error_log( "🔍 Existing backorders value for {$backorders_meta_key}: '{$existing_backorders}'" );

    // Try multiple approaches for updating backorders
    $backorders_updated = false;

    // Method 1: Standard update_post_meta
    $backorders_result = update_post_meta( $product_id, $backorders_meta_key, $backorders_value );
    if ( $backorders_result !== false ) {
        $backorders_updated = true;
        error_log( "✅ Method 1: Updated backorders meta {$backorders_meta_key} = {$backorders_value}" );
    } else {
        error_log( "⚠️ Method 1: update_post_meta returned false for {$backorders_meta_key}" );

        // Method 2: Delete and add
        delete_post_meta( $product_id, $backorders_meta_key );
        $add_result = add_post_meta( $product_id, $backorders_meta_key, $backorders_value, true );
        if ( $add_result ) {
            $backorders_updated = true;
            error_log( "✅ Method 2: Added backorders meta {$backorders_meta_key} = {$backorders_value}" );
        } else {
            error_log( "⚠️ Method 2: add_post_meta failed for {$backorders_meta_key}" );

            // Method 3: Direct database update
            global $wpdb;
            $db_result = $wpdb->replace(
                $wpdb->postmeta,
                [
                    'post_id' => $product_id,
                    'meta_key' => $backorders_meta_key,
                    'meta_value' => $backorders_value
                ]
            );

            if ( $db_result !== false ) {
                $backorders_updated = true;
                error_log( "✅ Method 3: Direct DB update for {$backorders_meta_key} = {$backorders_value}" );
            } else {
                error_log( "❌ Method 3: Direct DB update failed for {$backorders_meta_key}" );
            }
        }
    }

    if ( ! $backorders_updated ) {
        error_log( "❌ All methods failed to update backorders for region {$region}" );
        // Don't return error - continue with stock update success
        // return new WP_Error( 'backorders_update_failed', "Failed to update backorders setting for region {$region}" );
    }

    // Verify the updates
    $final_stock = get_post_meta( $product_id, $stock_meta_key, true );
    $final_backorders = get_post_meta( $product_id, $backorders_meta_key, true );
    error_log( "🔍 Final verification - Stock: {$final_stock}, Backorders: {$final_backorders}" );

    // Clear any relevant caches
    wp_cache_delete( $product_id, 'post_meta' );

    // If this is a WooCommerce product, clear WC caches too
    if ( function_exists( 'wc_delete_product_transients' ) ) {
        wc_delete_product_transients( $product_id );
    }

    error_log( "✅ Updated product {$product_id} stock for region {$region}: {$quantity} units, backorders: {$backorders_value}" );

    return [
        'region' => $region,
        'stock_quantity' => $quantity,
        'allow_backorders' => $allow_backorders,
        'stock_meta_key' => $stock_meta_key,
        'backorders_meta_key' => $backorders_meta_key,
        'backorders_updated' => $backorders_updated,
        'final_stock_value' => $final_stock,
        'final_backorders_value' => $final_backorders
    ];
}

/**
 * Helper function to get current stock levels for a product (for debugging)
 */
function get_product_stock_levels( $product_id ) {
    $stock_levels = [];
    
    // Get all meta for the product
    $all_meta = get_post_meta( $product_id );
    
    foreach ( $all_meta as $key => $value ) {
        if ( strpos( $key, '_stock_' ) === 0 || strpos( $key, '_backorders_' ) === 0 ) {
            $stock_levels[$key] = is_array( $value ) ? $value[0] : $value;
        }
    }

    return $stock_levels;
}

/**
 * Write to custom API log file
 */
function sap_inventory_log_api( $message ) {
    $log_dir = WP_CONTENT_DIR . '/logs';
    $log_file = $log_dir . '/APIlogs.log';

    // Create logs directory if it doesn't exist
    if ( ! file_exists( $log_dir ) ) {
        wp_mkdir_p( $log_dir );
    }

    // Format log entry with timestamp
    $timestamp = current_time( 'Y-m-d H:i:s' );
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;

    // Write to log file
    file_put_contents( $log_file, $log_entry, FILE_APPEND | LOCK_EX );

    // Also log to WordPress error log for backup (optional)
    error_log( $message );
}

/**
 * Get client IP address for logging
 */
function sap_inventory_get_client_ip() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

    foreach ( $ip_keys as $key ) {
        if ( ! empty( $_SERVER[$key] ) ) {
            $ip = sanitize_text_field( $_SERVER[$key] );
            // Handle comma-separated IPs (from proxies)
            if ( strpos( $ip, ',' ) !== false ) {
                $ip = trim( explode( ',', $ip )[0] );
            }
            if ( filter_var( $ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE ) ) {
                return $ip;
            }
        }
    }

    return $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
}

/**
 * Sanitize data for logging (remove sensitive information)
 */
function sap_inventory_sanitize_log_data( $data ) {
    if ( ! is_array( $data ) ) {
        return $data;
    }

    $sanitized = $data;

    // Remove or mask sensitive fields
    $sensitive_fields = ['password', 'token', 'secret', 'key'];

    array_walk_recursive( $sanitized, function( &$value, $key ) use ( $sensitive_fields ) {
        if ( in_array( strtolower( $key ), $sensitive_fields ) ) {
            $value = '[REDACTED]';
        }
    });

    return $sanitized;
}
