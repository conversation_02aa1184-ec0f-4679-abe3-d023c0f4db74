# SAP SoldTo Error Logging Enhancement

## Overview
Enhanced the SAP SoldTo plugin to track unsuccessful requests and errors in the API logs, providing comprehensive monitoring of authentication failures, validation errors, and other issues.

## Changes Made

### 1. Authentication Error Logging
**Location**: `sap_soldto_permission_check()` function (lines 75-153)

**Enhanced logging for**:
- `rest_not_logged_in` (401) - When no authenticated user is found
- `rest_forbidden` (403) - When user lacks sufficient permissions

**Log entries include**:
- Request ID for tracking
- Request method, URL, timestamp
- User agent and client IP
- Detailed error response in JSON format
- User information (when available)

### 2. Validation Error Logging
**Location**: `cuc_create_customer_endpoint()` function (lines 249-306)

**Enhanced logging for**:
- `invalid_data` (400) - No valid JSON data received
- `missing_soldto` (400) - soldTo data missing or invalid
- `missing_customer_id` (400) - customerId is required
- `invalid_email` (400) - Invalid email format

**Each validation error logs**:
- Complete error response in JSON format
- Human-readable error description
- Request ID for correlation

### 3. Database Error Logging
**Location**: `cuc_create_customer_endpoint()` function (lines 310-323)

**Enhanced logging for**:
- Database update failures
- Database insert failures
- Any WP_Error returned from database operations

**Logs include**:
- Full error response with code, message, and data
- Customer ID context
- Request ID for tracking

### 4. User Metadata Error Logging
**Location**: `cuc_create_customer_endpoint()` function (lines 350-366)

**Enhanced logging for**:
- WordPress user metadata update failures
- Individual user update errors within batch operations

**Logs include**:
- User ID and customer ID context
- Complete error details
- Request ID for correlation

### 5. Partial Failure Logging
**Location**: `cuc_create_customer_endpoint()` function (lines 372-385)

**Enhanced logging for**:
- Requests that succeed for some users but fail for others
- Summary of successful vs failed user updates
- Complete list of user errors

## Log File Location
All error logs are written to: `wp-content/logs/APIlogs-SoldTo.log`

## Error Response Format
All unsuccessful requests now log the complete error response in this format:
```json
{
    "code": "rest_not_logged_in",
    "message": "You are not currently logged in.",
    "data": {
        "status": 401
    }
}
```

## Log Entry Examples

### Authentication Failure
```
[2025-07-03 10:30:15] 🔐 SAP-SoldTo AUTH CHECK START [ID: sap_soldto_auth_66e5f2a7b8c9d]
[2025-07-03 10:30:15] ❌ SAP-SoldTo AUTH FAILED [ID: sap_soldto_auth_66e5f2a7b8c9d]: {
    "code": "rest_not_logged_in",
    "message": "You are not currently logged in.",
    "data": {
        "status": 401
    }
}
```

### Validation Error
```
[2025-07-03 10:30:20] ❌ SAP-SoldTo VALIDATION ERROR [ID: sap_soldto_66e5f2a7b8c9d]: {
    "code": "missing_customer_id",
    "message": "soldTo.customerId is required",
    "data": {
        "status": 400
    }
}
```

### Database Error
```
[2025-07-03 10:30:25] ❌ SAP-SoldTo DATABASE ERROR [ID: sap_soldto_66e5f2a7b8c9d]: {
    "code": "db_insert_failed",
    "message": "Failed to create database record",
    "data": {
        "status": 500
    }
}
```

## Testing
A test script `test-error-logging.php` has been created to verify all error logging functionality. The script tests:
1. Unauthenticated requests
2. Invalid JSON data
3. Missing soldTo data
4. Missing customerId
5. Invalid email format

## Benefits
1. **Complete Error Tracking**: All unsuccessful requests are now logged with full context
2. **Request Correlation**: Each request has a unique ID for tracking across log entries
3. **Structured Logging**: Consistent JSON format for easy parsing and analysis
4. **Comprehensive Context**: IP addresses, user agents, timestamps, and user information
5. **Monitoring Ready**: Logs can be easily monitored for patterns and issues
6. **Debugging Support**: Detailed error information helps with troubleshooting

## Monitoring Recommendations
1. Set up log rotation for the APIlogs-SoldTo.log file
2. Monitor for patterns in authentication failures
3. Alert on database errors or high error rates
4. Track validation errors to identify data quality issues
5. Use request IDs to correlate errors across different log entries
