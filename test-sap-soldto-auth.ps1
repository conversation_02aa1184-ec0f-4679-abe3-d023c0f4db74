# PowerShell script to test SAP SoldTo API with WordPress REST API authentication
# Usage: .\test-sap-soldto-auth.ps1

Write-Host "🚀 Testing SAP SoldTo API with WordPress REST API Authentication" -ForegroundColor Green
Write-Host ""

# Configuration - Update these values for your environment
$SiteUrl = "http://localhost/your-wordpress-site"  # Update this URL
$Username = "your_username"                        # Your WordPress username
$Password = "your_password"                        # Your WordPress password (or Application Password)

$ApiEndpoint = "$SiteUrl/wp-json/wc/v3/sap-soldto"
$TestEndpoint = "$SiteUrl/wp-json/wc/v3/sap-soldto-test"

Write-Host "Site URL: $SiteUrl" -ForegroundColor Yellow
Write-Host "API Endpoint: $ApiEndpoint" -ForegroundColor Yellow
Write-Host "Authentication: Using WordPress REST API (Basic Auth)" -ForegroundColor Yellow
Write-Host ""

# Check if credentials are set
if ($Username -eq "your_username" -or $Password -eq "your_password") {
    Write-Host "⚠️  WARNING: Please update the Username and Password variables!" -ForegroundColor Red
    Write-Host "   Use your WordPress admin username and password" -ForegroundColor Yellow
    Write-Host "   Or create an Application Password in WordPress Admin → Users → Your Profile" -ForegroundColor Yellow
    Write-Host ""
}

# Create Basic Auth credentials
$auth = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${Username}:${Password}"))
$headers = @{
    "Authorization" = "Basic $auth"
    "Content-Type" = "application/json"
}

Write-Host "🔑 Using Basic Authentication with WordPress credentials" -ForegroundColor Cyan
Write-Host ""

# Test 1: Check if the API is active
Write-Host "📡 Test 1: Checking API Status..." -ForegroundColor Cyan

try {
    # Test endpoint doesn't require auth, but let's test it anyway
    $testResponse = Invoke-RestMethod -Uri $TestEndpoint -Method Get -ContentType "application/json"
    Write-Host "✅ API Status: " -ForegroundColor Green -NoNewline
    Write-Host $testResponse.message -ForegroundColor White
    Write-Host "   Timestamp: $($testResponse.timestamp)" -ForegroundColor Gray
    Write-Host "   Auth Required: $($testResponse.auth_required)" -ForegroundColor Gray
} catch {
    Write-Host "❌ API Test Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Make sure WordPress is running and the plugin is activated." -ForegroundColor Yellow
    exit 1
}

Write-Host ""

# Test 2: Send sample SoldTo data
Write-Host "👤 Test 2: Creating Sample Customer..." -ForegroundColor Cyan

# Generate unique test data
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$testCustomerId = "TEST_$timestamp"
$testEmail = "test_$<EMAIL>"

$testData = @{
    soldTo = @{
        customerId = $testCustomerId
        email = $testEmail
        companyCode = "1000"
        countryCode = "US"
        priceGroup = "STANDARD"
        firstName = "Test"
        lastName = "Customer"
        company = "Test Company Inc"
    }
    billingAddress = @{
        company = "Test Company Inc"
        address = @{
            line1 = "123 Test Street"
            line2 = "Suite 456"
            city = "Test City"
            postcode = "12345"
            countryRegion = "US"
            stateCounty = "NY"
        }
    }
    shiptos = @(
        "WAREHOUSE_001",
        "WAREHOUSE_002"
    )
} | ConvertTo-Json -Depth 10

Write-Host "Sending data:" -ForegroundColor Yellow
Write-Host $testData -ForegroundColor Gray
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $testData -Headers $headers
    
    Write-Host "✅ SUCCESS: Customer created successfully!" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Response Details:" -ForegroundColor Yellow
    Write-Host "  Success: $($response.success)" -ForegroundColor White
    Write-Host "  User ID: $($response.user_id)" -ForegroundColor White
    Write-Host "  Username: $($response.username)" -ForegroundColor White
    Write-Host "  Customer ID: $testCustomerId" -ForegroundColor White
    
    if ($response.debug_info) {
        Write-Host "  Debug Info:" -ForegroundColor White
        Write-Host "    Customer ID Saved: $($response.debug_info.customer_id_saved)" -ForegroundColor Gray
        Write-Host "    Endpoint: $($response.debug_info.endpoint)" -ForegroundColor Gray
    }
    
    Write-Host ""
    Write-Host "Full Response:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 5 | Write-Host -ForegroundColor Gray
    
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    $errorBody = ""
    
    try {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        $reader.Close()
        $errorStream.Close()
    } catch {
        $errorBody = "Could not read error response"
    }
    
    Write-Host "❌ ERROR: HTTP Status $statusCode" -ForegroundColor Red
    Write-Host "Error Details: $errorBody" -ForegroundColor Red
    
    if ($statusCode -eq 401) {
        Write-Host "   Authentication failed. Check your username/password." -ForegroundColor Yellow
    } elseif ($statusCode -eq 403) {
        Write-Host "   Access forbidden. Make sure your user has administrator privileges." -ForegroundColor Yellow
    } elseif ($statusCode -eq 404) {
        Write-Host "   The endpoint was not found. Make sure the plugin is activated." -ForegroundColor Yellow
    } elseif ($statusCode -eq 500) {
        Write-Host "   Server error. Check WordPress error logs for details." -ForegroundColor Yellow
    } elseif ($statusCode -eq 400) {
        Write-Host "   Bad request. The customer might already exist." -ForegroundColor Yellow
    }
}

Write-Host ""

# Test 3: Test with duplicate customer (should fail)
Write-Host "🔍 Test 3: Testing Duplicate Customer Handling..." -ForegroundColor Cyan

try {
    $duplicateResponse = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $testData -Headers $headers
    
    Write-Host "⚠️  Unexpected: Duplicate customer was allowed" -ForegroundColor Yellow
    
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    
    if ($statusCode -eq 400) {
        Write-Host "✅ Duplicate handling works correctly - API returned 400 Bad Request" -ForegroundColor Green
    } else {
        Write-Host "❌ Unexpected error for duplicate: HTTP $statusCode" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎉 Testing Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Check WordPress error logs for detailed processing information" -ForegroundColor White
Write-Host "2. Verify the customer was created in WordPress Users" -ForegroundColor White
Write-Host "3. Check user metadata for SAP fields (_customer, _companycode, etc.)" -ForegroundColor White
Write-Host "4. Test with real SAP data from your system" -ForegroundColor White
Write-Host "5. Update the site URL in this script for your environment" -ForegroundColor White
Write-Host ""
Write-Host "Authentication is now secure with WordPress REST API! 🔐" -ForegroundColor Cyan
