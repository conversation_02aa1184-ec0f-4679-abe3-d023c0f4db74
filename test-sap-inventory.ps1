# PowerShell script to test SAP Inventory API
# Usage: .\test-sap-inventory.ps1

Write-Host "🚀 Testing SAP Inventory API" -ForegroundColor Green
Write-Host ""

# Configuration - Update these values for your environment
$SiteUrl = "http://localhost/your-wordpress-site"  # Update this URL
$Username = "your_username"                        # Your WordPress username
$Password = "your_password"                        # Your WordPress password (or Application Password)

$ApiEndpoint = "$SiteUrl/wp-json/wc/v3/sap-inventory"
$TestEndpoint = "$SiteUrl/wp-json/wc/v3/sap-inventory-test"

Write-Host "Site URL: $SiteUrl" -ForegroundColor Yellow
Write-Host "API Endpoint: $ApiEndpoint" -ForegroundColor Yellow
Write-Host "Authentication: Using WordPress REST API (Basic Auth)" -ForegroundColor Yellow
Write-Host ""

# Check if credentials are set
if ($Username -eq "your_username" -or $Password -eq "your_password") {
    Write-Host "⚠️  WARNING: Please update the Username and Password variables!" -ForegroundColor Red
    Write-Host "   Use your WordPress admin username and password" -ForegroundColor Yellow
    Write-Host "   Or create an Application Password in WordPress Admin → Users → Your Profile" -ForegroundColor Yellow
    Write-Host ""
}

# Create Basic Auth credentials
$auth = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${Username}:${Password}"))
$headers = @{
    "Authorization" = "Basic $auth"
    "Content-Type" = "application/json"
}

Write-Host "🔑 Using Basic Authentication with WordPress credentials" -ForegroundColor Cyan
Write-Host ""

# Test 1: Check if the API is active
Write-Host "📡 Test 1: Checking API Status..." -ForegroundColor Cyan

try {
    $testResponse = Invoke-RestMethod -Uri $TestEndpoint -Method Get -ContentType "application/json"
    Write-Host "✅ API Status: " -ForegroundColor Green -NoNewline
    Write-Host $testResponse.message -ForegroundColor White
    Write-Host "   Timestamp: $($testResponse.timestamp)" -ForegroundColor Gray
    Write-Host "   Auth Required: $($testResponse.auth_required)" -ForegroundColor Gray
} catch {
    Write-Host "❌ API Test Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Make sure WordPress is running and the plugin is activated." -ForegroundColor Yellow
    exit 1
}

Write-Host ""

# Test 2: Send sample inventory data
Write-Host "📦 Test 2: Updating Product Inventory..." -ForegroundColor Cyan
Write-Host "   Note: Make sure you have a product with title 'M12345' in your WooCommerce store" -ForegroundColor Yellow

$testData = @{
    inventory = @{
        materialNumber = "M12345"
        stock = @{
            US = @{
                quantity = 100
                allowBackorders = $false
            }
            EU = @{
                quantity = 200
                allowBackorders = $true
            }
            CA = @{
                quantity = 50
                allowBackorders = $false
            }
        }
    }
} | ConvertTo-Json -Depth 10

Write-Host "Sending data:" -ForegroundColor Yellow
Write-Host $testData -ForegroundColor Gray
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $testData -Headers $headers
    
    Write-Host "✅ SUCCESS: Inventory updated successfully!" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Response Details:" -ForegroundColor Yellow
    Write-Host "  Success: $($response.success)" -ForegroundColor White
    Write-Host "  Product ID: $($response.product_id)" -ForegroundColor White
    Write-Host "  Material Number: $($response.material_number)" -ForegroundColor White
    Write-Host "  Regions Updated: $($response.total_regions_updated)" -ForegroundColor White
    
    if ($response.updated_regions) {
        Write-Host "  Updated Regions:" -ForegroundColor White
        foreach ($region in $response.updated_regions) {
            Write-Host "    - $($region.region): $($region.stock_quantity) units, backorders: $($region.allow_backorders)" -ForegroundColor Gray
        }
    }
    
    if ($response.errors -and $response.errors.Count -gt 0) {
        Write-Host "  Errors:" -ForegroundColor Red
        foreach ($error in $response.errors) {
            Write-Host "    - $error" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "Full Response:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 5 | Write-Host -ForegroundColor Gray
    
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    $errorBody = ""
    
    try {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        $reader.Close()
        $errorStream.Close()
    } catch {
        $errorBody = "Could not read error response"
    }
    
    Write-Host "❌ ERROR: HTTP Status $statusCode" -ForegroundColor Red
    Write-Host "Error Details: $errorBody" -ForegroundColor Red
    
    if ($statusCode -eq 401) {
        Write-Host "   Authentication failed. Check your username/password." -ForegroundColor Yellow
    } elseif ($statusCode -eq 403) {
        Write-Host "   Access forbidden. Make sure your user has administrator privileges." -ForegroundColor Yellow
    } elseif ($statusCode -eq 404) {
        Write-Host "   Product not found. Make sure you have a product with title 'M12345'." -ForegroundColor Yellow
        Write-Host "   Or update the materialNumber in the test data to match an existing product." -ForegroundColor Yellow
    } elseif ($statusCode -eq 500) {
        Write-Host "   Server error. Check WordPress error logs for details." -ForegroundColor Yellow
    } elseif ($statusCode -eq 400) {
        Write-Host "   Bad request. Check the data format." -ForegroundColor Yellow
    }
}

Write-Host ""

# Test 3: Test with different material number
Write-Host "🔍 Test 3: Testing Different Material Number..." -ForegroundColor Cyan

$testData2 = @{
    inventory = @{
        materialNumber = "PRODUCT_XYZ"
        stock = @{
            US = @{
                quantity = 75
                allowBackorders = $true
            }
        }
    }
} | ConvertTo-Json -Depth 10

try {
    $response2 = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $testData2 -Headers $headers
    
    Write-Host "✅ Second product updated successfully!" -ForegroundColor Green
    Write-Host "   Product ID: $($response2.product_id)" -ForegroundColor White
    
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    
    if ($statusCode -eq 404) {
        Write-Host "✅ Product not found handling works correctly - API returned 404" -ForegroundColor Green
        Write-Host "   This is expected if you don't have a product titled 'PRODUCT_XYZ'" -ForegroundColor Gray
    } else {
        Write-Host "❌ Unexpected error for second test: HTTP $statusCode" -ForegroundColor Red
    }
}

Write-Host ""

# Test 4: Test with invalid data
Write-Host "🔍 Test 4: Testing Error Handling..." -ForegroundColor Cyan

$invalidData = @{
    inventory = @{
        # Missing materialNumber - should cause an error
        stock = @{
            US = @{
                quantity = 10
            }
        }
    }
} | ConvertTo-Json -Depth 5

try {
    $errorResponse = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $invalidData -Headers $headers
    
    Write-Host "⚠️  Expected error but got success response" -ForegroundColor Yellow
    
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    
    if ($statusCode -eq 400) {
        Write-Host "✅ Error handling works correctly - API returned 400 Bad Request" -ForegroundColor Green
    } else {
        Write-Host "❌ Unexpected error status: HTTP $statusCode" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎉 Testing Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Check WordPress error logs for detailed processing information" -ForegroundColor White
Write-Host "2. Verify inventory was updated in WooCommerce products" -ForegroundColor White
Write-Host "3. Check product custom fields: _stock_us, _stock_eu, _backorders_us, etc." -ForegroundColor White
Write-Host "4. Test with real material numbers from your WooCommerce store" -ForegroundColor White
Write-Host "5. Update the site URL in this script for your environment" -ForegroundColor White
Write-Host ""
Write-Host "Custom Fields Created:" -ForegroundColor Cyan
Write-Host "  _stock_us, _stock_eu, _stock_ca" -ForegroundColor White
Write-Host "  _backorders_us, _backorders_eu, _backorders_ca" -ForegroundColor White
