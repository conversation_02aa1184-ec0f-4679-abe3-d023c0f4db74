<?php
/**
 * Plugin Name: SAP Sold TO API - DEBUG VERSION
 * Description: Creates WP users from JSON and adds all SoldTo/billing fields as user meta. DEBUG VERSION WITH NO AUTH.
 * Version:     1.5-DEBUG
 * Author:      ATAK Interactive
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Register REST route with NO AUTHENTICATION (for debugging only)
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-soldto-debug', [
        'methods'             => 'POST',
        'callback'            => 'cuc_create_customer_endpoint_debug',
        'permission_callback' => '__return_true', // ALLOW ALL REQUESTS - REMOVE IN PRODUCTION!
    ] );
} );

/**
 * Debug version of endpoint - same logic but with no auth requirements
 */
function cuc_create_customer_endpoint_debug( WP_REST_Request $request ) {
    error_log( "🔍 SAP-SoldTo DEBUG API called" );
    
    $data = $request->get_json_params();
    
    // Log the incoming data for debugging
    error_log( "🔍 SAP-SoldTo DEBUG API received data: " . print_r( $data, true ) );

    // — Enhanced Validation —
    if ( empty( $data ) || ! is_array( $data ) ) {
        error_log( "❌ No valid JSON data received" );
        return new WP_Error( 'invalid_data', 'No valid JSON data received', [ 'status'=>400 ] );
    }
    
    if ( empty( $data['soldTo'] ) || ! is_array( $data['soldTo'] ) ) {
        error_log( "❌ soldTo data is missing or invalid" );
        return new WP_Error( 'missing_soldto', 'soldTo data is required and must be an object', [ 'status'=>400 ] );
    }
    
    if ( empty( $data['soldTo']['customerId'] ) ) {
        error_log( "❌ customerId is missing" );
        return new WP_Error( 'missing_customer_id', 'soldTo.customerId is required', [ 'status'=>400 ] );
    }
    if ( empty( $data['soldTo']['email'] ) || ! is_email( $data['soldTo']['email'] ) ) {
        error_log( "❌ Invalid email: " . ( $data['soldTo']['email'] ?? 'empty' ) );
        return new WP_Error( 'invalid_email', 'A valid soldTo.email is required', [ 'status'=>400 ] );
    }

    // — Create user —
    $email    = sanitize_email( $data['soldTo']['email'] );
    $username = sanitize_user( $email, true );
    
    error_log( "🔍 Attempting to create user with email: {$email}, username: {$username}" );
    
    if ( username_exists( $username ) || email_exists( $email ) ) {
        error_log( "❌ User already exists: {$email}" );
        return new WP_Error( 'user_exists', 'User/email already exists', [ 'status'=>400 ] );
    }

    $password = wp_generate_password( 12, false );
    $user_id  = wp_insert_user([
        'user_login' => $username,
        'user_pass'  => $password,
        'user_email'=> $email,
        'role'       => 'customer',
    ]);
    
    if ( is_wp_error( $user_id ) ) {
        error_log( "❌ Failed to create user: " . $user_id->get_error_message() );
        return $user_id;
    }
    
    error_log( "✅ User created successfully with ID: {$user_id}" );

    // — Clear caches —
    wp_cache_delete( $user_id, 'user_meta' );
    wp_cache_delete( $user_id, 'users' );

    // — Add SoldTo meta —
    $sold = $data['soldTo'];
    error_log( "🔍 Adding SoldTo metadata for user {$user_id}" );
    
    $customer_id = sanitize_text_field( $sold['customerId'] );
    $result = add_user_meta( $user_id, 'customer_id', $customer_id );
    if ( $result === false ) {
        $result = update_user_meta( $user_id, 'customer_id', $customer_id );
    }
    error_log( "✅ Customer ID result: " . var_export($result, true) );

    // — Verify metadata was saved —
    $saved_customer_id = get_user_meta( $user_id, 'customer_id', true );
    error_log( "🔍 Verification - saved customer_id: " . $saved_customer_id );

    // — Response —
    return rest_ensure_response([
        'success'  => true,
        'user_id'  => $user_id,
        'username' => $username,
        'password' => $password,
        'debug_info' => [
            'customer_id_saved' => $saved_customer_id,
            'endpoint' => 'debug-version'
        ]
    ]);
}
