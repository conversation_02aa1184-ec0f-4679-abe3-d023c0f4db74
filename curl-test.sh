#!/bin/bash

# cURL test script for SAP SoldTo API endpoints
# Usage: ./curl-test.sh [your-wordpress-site-url] [username] [password]

SITE_URL=${1:-"http://localhost"}
USERNAME=${2:-"admin"}
PASSWORD=${3:-"password"}

ORIGINAL_ENDPOINT="${SITE_URL}/wp-json/wc/v3/sap-soldto"
DEBUG_ENDPOINT="${SITE_URL}/wp-json/wc/v3/sap-soldto-debug"

# Generate unique test data
TIMESTAMP=$(date +%s)
TEST_EMAIL="test${TIMESTAMP}@example.com"
CUSTOMER_ID="TEST_CUSTOMER_${TIMESTAMP}"

echo "Testing SAP SoldTo API Endpoints"
echo "================================="
echo "Site URL: ${SITE_URL}"
echo "Username: ${USERNAME}"
echo "Test Email: ${TEST_EMAIL}"
echo "Customer ID: ${CUSTOMER_ID}"
echo ""

# Test data
TEST_DATA='{
  "soldTo": {
    "customerId": "'${CUSTOMER_ID}'",
    "email": "'${TEST_EMAIL}'",
    "companyCode": "TEST_COMP",
    "countryCode": "US",
    "priceGroup": "STANDARD"
  },
  "billingAddress": {
    "company": "Test Company Inc.",
    "address": {
      "line1": "123 Test Street",
      "line2": "Suite 456",
      "city": "Test City",
      "postcode": "12345",
      "countryRegion": "US",
      "stateCounty": "CA"
    }
  },
  "shiptos": ["SHIPTO001", "SHIPTO002"]
}'

echo "Testing Original Endpoint (with auth)"
echo "====================================="

# Make the request to original endpoint
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic $(echo -n "${USERNAME}:${PASSWORD}" | base64)" \
  -d "${TEST_DATA}" \
  "${ORIGINAL_ENDPOINT}" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo ""
echo ""
echo "Testing Debug Endpoint (no auth required)"
echo "=========================================="

# Generate new test data for debug endpoint
TIMESTAMP2=$((TIMESTAMP + 1))
TEST_EMAIL2="test${TIMESTAMP2}@example.com"
CUSTOMER_ID2="TEST_CUSTOMER_${TIMESTAMP2}"

TEST_DATA2='{
  "soldTo": {
    "customerId": "'${CUSTOMER_ID2}'",
    "email": "'${TEST_EMAIL2}'",
    "companyCode": "TEST_COMP",
    "countryCode": "US",
    "priceGroup": "STANDARD"
  },
  "billingAddress": {
    "company": "Test Company Inc.",
    "address": {
      "line1": "123 Test Street",
      "line2": "Suite 456",
      "city": "Test City",
      "postcode": "12345",
      "countryRegion": "US",
      "stateCounty": "CA"
    }
  },
  "shiptos": ["SHIPTO001", "SHIPTO002"]
}'

# Make the request to debug endpoint (no auth)
curl -X POST \
  -H "Content-Type: application/json" \
  -d "${TEST_DATA2}" \
  "${DEBUG_ENDPOINT}" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo ""
echo ""
echo "Usage: $0 [site_url] [username] [password]"
echo "Example: $0 http://localhost admin mypassword"
