<?php
/**
 * Simple validation test for SAP SoldTo API endpoint
 * Tests the data validation logic without requiring WordPress
 */

// Mock WordPress functions for testing
function sanitize_email($email) { return filter_var($email, FILTER_SANITIZE_EMAIL); }
function sanitize_user($user, $strict = false) { return preg_replace('/[^a-zA-Z0-9._@-]/', '', $user); }
function sanitize_text_field($str) { return trim(strip_tags($str)); }
function is_email($email) { return filter_var($email, FILTER_VALIDATE_EMAIL) !== false; }

echo "SAP SoldTo API - Data Validation Test\n";
echo "====================================\n\n";

function test_data_validation() {
    // Sample test data
    $test_data = [
        'soldTo' => [
            'customerId' => 'TEST_CUSTOMER_' . time(),
            'email' => 'test' . time() . '@example.com',
            'companyCode' => 'TEST_COMP',
            'countryCode' => 'US',
            'priceGroup' => 'STANDARD'
        ],
        'billingAddress' => [
            'company' => 'Test Company Inc.',
            'address' => [
                'line1' => '123 Test Street',
                'line2' => 'Suite 456',
                'city' => 'Test City',
                'postcode' => '12345',
                'countryRegion' => 'US',
                'stateCounty' => 'CA'
            ]
        ],
        'shiptos' => ['SHIPTO001', 'SHIPTO002']
    ];

    echo "Test Data:\n";
    echo print_r($test_data, true) . "\n";

    // Test validation logic
    echo "Validation Tests:\n";
    echo "================\n";

    // Test 1: Check if data structure is valid
    if (empty($test_data) || !is_array($test_data)) {
        echo "❌ No valid data received\n";
        return false;
    } else {
        echo "✅ Valid data structure\n";
    }

    // Test 2: Check soldTo structure
    if (empty($test_data['soldTo']) || !is_array($test_data['soldTo'])) {
        echo "❌ soldTo data is missing or invalid\n";
        return false;
    } else {
        echo "✅ Valid soldTo structure\n";
    }

    // Test 3: Check required fields
    if (empty($test_data['soldTo']['customerId'])) {
        echo "❌ customerId is missing\n";
        return false;
    } else {
        echo "✅ customerId present: " . $test_data['soldTo']['customerId'] . "\n";
    }

    if (empty($test_data['soldTo']['email']) || !is_email($test_data['soldTo']['email'])) {
        echo "❌ Invalid email\n";
        return false;
    } else {
        echo "✅ Valid email: " . $test_data['soldTo']['email'] . "\n";
    }

    // Test 4: Data sanitization
    echo "\nData Sanitization Tests:\n";
    echo "=======================\n";

    $email = sanitize_email($test_data['soldTo']['email']);
    $username = sanitize_user($email, true);
    $customer_id = sanitize_text_field($test_data['soldTo']['customerId']);

    echo "✅ Sanitized email: {$email}\n";
    echo "✅ Generated username: {$username}\n";
    echo "✅ Sanitized customer_id: {$customer_id}\n";

    // Test 5: Billing address processing
    echo "\nBilling Address Processing:\n";
    echo "==========================\n";

    $bill = is_array($test_data['billingAddress']) ? $test_data['billingAddress'] : [];
    $addr = is_array($bill['address']) ? $bill['address'] : [];

    $billing_fields = [
        'billing_company' => $bill['company'] ?? '',
        'billing_address_1' => $addr['line1'] ?? '',
        'billing_city' => $addr['city'] ?? '',
        'billing_postcode' => $addr['postcode'] ?? '',
        'billing_country' => $addr['countryRegion'] ?? '',
    ];

    foreach ($billing_fields as $key => $value) {
        if (!empty($value)) {
            echo "✅ {$key}: " . sanitize_text_field($value) . "\n";
        }
    }

    echo "\n✅ All validation tests passed!\n";
    return true;
}

// Run the test
test_data_validation();
?>
