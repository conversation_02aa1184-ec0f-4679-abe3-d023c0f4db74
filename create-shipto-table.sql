-- SQL to create SAP ShipTo Addresses table
-- This table stores shipping addresses in serialized WCMCA format for direct compatibility

CREATE TABLE wp_sap_shipto_addresses (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    customer_id varchar(50) NOT NULL,

    -- WCMCA compatible serialized addresses data
    -- Stores the exact same format as _wcmca_additional_addresses metadata
    wcmca_addresses_data longtext DEFAULT NULL,

    -- Original SAP JSON data for reference and debugging
    sap_raw_data longtext DEFAULT NULL,

    -- Summary information for quick queries
    total_addresses int(11) DEFAULT 0,
    has_default_address tinyint(1) DEFAULT 0,

    -- WordPress integration
    wp_user_id bigint(20) DEFAULT NULL,

    -- Timestamps
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Status
    status varchar(20) DEFAULT 'active',

    -- Indexes
    PRIMARY KEY (id),
    UNIQUE KEY customer_id (customer_id),
    KEY wp_user_id (wp_user_id),
    KEY status (status),
    <PERSON><PERSON><PERSON> has_default_address (has_default_address),
    KEY total_addresses (total_addresses),
    KEY created_at (created_at),
    KEY updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Comments for documentation
ALTER TABLE wp_sap_shipto_addresses
COMMENT = 'SAP ShipTo addresses stored in serialized WCMCA format for direct compatibility with WordPress metadata';

-- Sample data structure comment
/*
This table stores shipping addresses in the exact same format as WordPress metadata:

wcmca_addresses_data contains serialized PHP array like:
a:2:{
  i:0;a:9:{
    s:4:"type";s:8:"shipping";
    s:10:"address_id";i:1128545;
    s:21:"address_internal_name";s:14:"Main Warehouse";
    s:27:"shipping_is_default_address";s:1:"1";
    s:16:"shipping_country";s:2:"US";
    s:18:"shipping_address_1";s:10:"Elm Street";
    s:13:"shipping_city";s:10:"Springwood";
    s:17:"shipping_postcode";s:3:"111";
    s:14:"shipping_state";s:2:"NY";
  }
  i:1;a:9:{
    s:4:"type";s:8:"shipping";
    s:10:"address_id";s:13:"68659ec127a3a";
    s:21:"address_internal_name";s:19:"Secondary Warehouse";
    s:16:"shipping_country";s:2:"US";
    s:18:"shipping_address_1";s:13:"Pine Road 99B";
    s:18:"shipping_address_2";s:6:"Unit 5";
    s:13:"shipping_city";s:10:"Springwood";
    s:17:"shipping_postcode";s:3:"222";
    s:14:"shipping_state";s:2:"CA";
  }
}

Benefits:
- One row per customer (not per address)
- Direct compatibility with WCMCA plugin
- Can copy wcmca_addresses_data directly to _wcmca_additional_addresses
- Preserves original SAP data for reference
- Summary fields for quick queries without deserializing
*/
