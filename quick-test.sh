#!/bin/bash

# Quick test script for SAP SoldTo API
# Usage: ./quick-test.sh [your-site-url]

SITE_URL=${1:-"http://localhost"}

echo "Quick SAP SoldTo API Test"
echo "========================="
echo "Site: $SITE_URL"
echo ""

echo "1. Testing if endpoint is registered (GET test endpoint):"
echo "--------------------------------------------------------"
curl -s "${SITE_URL}/wp-json/wc/v3/sap-soldto-test" | python -m json.tool 2>/dev/null || curl -s "${SITE_URL}/wp-json/wc/v3/sap-soldto-test"

echo ""
echo ""

echo "2. Testing main endpoint with minimal data:"
echo "-------------------------------------------"

TIMESTAMP=$(date +%s)
TEST_DATA="{
  \"soldTo\": {
    \"customerId\": \"TEST_${TIMESTAMP}\",
    \"email\": \"test${TIMESTAMP}@example.com\"
  }
}"

echo "Sending: $TEST_DATA"
echo ""

RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
  -X POST \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA" \
  "${SITE_URL}/wp-json/wc/v3/sap-soldto")

HTTP_STATUS=$(echo $RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
HTTP_BODY=$(echo $RESPONSE | sed -e 's/HTTPSTATUS\:.*//g')

echo "HTTP Status: $HTTP_STATUS"
echo "Response Body:"
echo "$HTTP_BODY" | python -m json.tool 2>/dev/null || echo "$HTTP_BODY"

echo ""
echo ""

if [ "$HTTP_STATUS" -eq 200 ]; then
    echo "✅ SUCCESS: Endpoint is working!"
    echo "Check your WordPress error logs for detailed debugging info."
elif [ "$HTTP_STATUS" -eq 404 ]; then
    echo "❌ ERROR: Endpoint not found. Make sure the plugin is activated."
elif [ "$HTTP_STATUS" -eq 500 ]; then
    echo "❌ ERROR: Server error. Check WordPress error logs."
else
    echo "⚠️  HTTP Status $HTTP_STATUS - Check the response above."
fi

echo ""
echo "Next steps:"
echo "- Check WordPress error logs for messages starting with 🚀 or 🔍"
echo "- If you see 'SAP-SoldTo API ENDPOINT CALLED!' then the endpoint is working"
echo "- Look for user creation and metadata saving logs"
