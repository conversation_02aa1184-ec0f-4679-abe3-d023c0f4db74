<?php
/**
 * Test script to verify the correct field mapping is working
 */

// Load WordPress
if (!function_exists('get_user_meta')) {
    if (file_exists('./wp-config.php')) {
        require_once('./wp-config.php');
    } elseif (file_exists('../wp-config.php')) {
        require_once('../wp-config.php');
    } else {
        die('WordPress not found.');
    }
}

echo "<h1>Correct Field Mapping Test</h1>\n";

// Get the most recent users
$users = get_users([
    'orderby' => 'registered',
    'order' => 'DESC',
    'number' => 5
]);

if (empty($users)) {
    echo "<p>❌ No users found</p>\n";
    exit;
}

echo "<h2>Recent Users - Checking for Correct Field Names</h2>\n";

$correct_fields = [
    '_customer' => 'Customer ID',
    '_companycode' => 'Company Code',
    '_country' => 'Country Code',
    '_pricegroup' => 'Price Group',
    '_shiptos' => 'Ship-To Locations'
];

foreach ($users as $user) {
    echo "<h3>User: {$user->user_login} (ID: {$user->ID})</h3>\n";
    echo "<p>Email: {$user->user_email}</p>\n";
    echo "<p>Registered: {$user->user_registered}</p>\n";
    echo "<p>Roles: " . implode(', ', $user->roles) . "</p>\n";
    
    echo "<h4>Custom Fields (Correct Format):</h4>\n";
    $has_custom_fields = false;
    
    foreach ($correct_fields as $field_key => $field_name) {
        $value = get_user_meta($user->ID, $field_key, true);
        if (!empty($value)) {
            if ($field_key === '_shiptos' && is_array($value)) {
                echo "<p>✅ {$field_name} ({$field_key}): <strong>" . implode(', ', $value) . "</strong></p>\n";
            } else {
                echo "<p>✅ {$field_name} ({$field_key}): <strong>{$value}</strong></p>\n";
            }
            $has_custom_fields = true;
        } else {
            echo "<p>❌ {$field_name} ({$field_key}): <em>Not set</em></p>\n";
        }
    }
    
    if ($has_custom_fields) {
        echo "<p style='background-color: lightgreen; padding: 10px;'>✅ This user has custom fields in the correct format!</p>\n";
    } else {
        echo "<p style='background-color: lightcoral; padding: 10px;'>❌ This user does not have custom fields in the correct format.</p>\n";
    }
    
    echo "<hr>\n";
}

echo "<h2>Field Mapping Reference</h2>\n";
echo "<p>The correct field mapping based on your database is:</p>\n";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr><th>JSON Field</th><th>WordPress Meta Key</th><th>Description</th></tr>\n";
echo "<tr><td>soldTo.customerId</td><td>_customer</td><td>Customer ID</td></tr>\n";
echo "<tr><td>soldTo.companyCode</td><td>_companycode</td><td>Company Code</td></tr>\n";
echo "<tr><td>soldTo.countryCode</td><td>_country</td><td>Country Code</td></tr>\n";
echo "<tr><td>soldTo.priceGroup</td><td>_pricegroup</td><td>Price Group</td></tr>\n";
echo "<tr><td>shiptos (array)</td><td>_shiptos</td><td>Ship-To Locations</td></tr>\n";
echo "</table>\n";

echo "<h2>Test Data for API</h2>\n";
echo "<p>Use this test data to verify the API is working:</p>\n";
echo "<pre>\n";
echo "{\n";
echo '  "soldTo": {' . "\n";
echo '    "customerId": "TEST_' . time() . '",' . "\n";
echo '    "email": "test' . time() . '@example.com",' . "\n";
echo '    "companyCode": "COMP_TEST",' . "\n";
echo '    "countryCode": "US",' . "\n";
echo '    "priceGroup": "STANDARD"' . "\n";
echo "  },\n";
echo '  "shiptos": ["1128545", "1131356", "1131487"]' . "\n";
echo "}\n";
echo "</pre>\n";
?>
