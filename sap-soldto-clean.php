<?php
/**
 * Plugin Name: SAP Sold TO API - Clean Version
 * Description: Creates WP users with problematic hooks temporarily disabled
 * Version:     1.7-CLEAN
 * Author:      ATAK Interactive
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-soldto-clean', [
        'methods'             => 'POST',
        'callback'            => 'cuc_create_customer_clean',
        'permission_callback' => '__return_true',
    ] );
} );

/**
 * Clean endpoint that temporarily disables problematic hooks
 */
function cuc_create_customer_clean( WP_REST_Request $request ) {
    error_log( "🚀 SAP-SoldTo CLEAN API called" );
    
    $data = $request->get_json_params();
    error_log( "🔍 Received data: " . print_r( $data, true ) );

    // Validation
    if ( empty( $data['soldTo']['customerId'] ) || empty( $data['soldTo']['email'] ) ) {
        return new WP_Error( 'missing_data', 'customerId and email are required', [ 'status' => 400 ] );
    }

    $email = sanitize_email( $data['soldTo']['email'] );
    $username = sanitize_user( $email, true );
    $customer_id = sanitize_text_field( $data['soldTo']['customerId'] );
    
    // Check if user exists
    if ( username_exists( $username ) || email_exists( $email ) ) {
        return new WP_Error( 'user_exists', 'User already exists', [ 'status' => 400 ] );
    }

    // Store original hooks so we can restore them
    global $wp_filter;
    $original_hooks = [];
    
    // List of problematic hooks identified from diagnostics
    $problematic_hooks = [
        'Addify_Registration_Fields_Addon_Front::afreg_save_extra_fields',
        'save_custom_user_fields',
        'Hubwoo_Public::hubwoo_woocommerce_save_account_details',
        'Hubwoo_Public::hubwoo_abncart_user_registeration',
        'ACF_Form_User::save_user'
    ];
    
    // Temporarily remove problematic hooks
    error_log( "🔍 Temporarily disabling problematic hooks" );
    if ( isset( $wp_filter['user_register'] ) ) {
        $original_hooks = $wp_filter['user_register'];
        
        // Remove specific problematic callbacks
        remove_action( 'user_register', array( 'Addify_Registration_Fields_Addon_Front', 'afreg_save_extra_fields' ), 10 );
        remove_action( 'user_register', 'save_custom_user_fields', 10 );
        remove_action( 'user_register', array( 'Hubwoo_Public', 'hubwoo_woocommerce_save_account_details' ), 10 );
        remove_action( 'user_register', array( 'Hubwoo_Public', 'hubwoo_abncart_user_registeration' ), 10 );
        remove_action( 'user_register', array( 'ACF_Form_User', 'save_user' ), 10 );
        
        error_log( "🔍 Hooks disabled, remaining hooks: " . count( $wp_filter['user_register']->callbacks ?? [] ) );
    }

    // Create user
    $password = wp_generate_password( 12, false );
    $user_data = [
        'user_login' => $username,
        'user_pass'  => $password,
        'user_email' => $email,
        'role'       => 'customer',
    ];
    
    error_log( "🔍 Creating user with cleaned hooks" );
    $user_id = wp_insert_user( $user_data );
    
    // Restore original hooks immediately
    if ( !empty( $original_hooks ) ) {
        $wp_filter['user_register'] = $original_hooks;
        error_log( "🔍 Original hooks restored" );
    }
    
    if ( is_wp_error( $user_id ) ) {
        error_log( "❌ User creation failed: " . $user_id->get_error_message() );
        return $user_id;
    }
    
    error_log( "✅ User created successfully with ID: {$user_id}" );

    // Add metadata
    $metadata = [
        'customer_id'  => $customer_id,
        'company_code' => $data['soldTo']['companyCode'] ?? '',
        'country_code' => $data['soldTo']['countryCode'] ?? '',
        'price_group'  => $data['soldTo']['priceGroup'] ?? '',
    ];
    
    // Add billing address
    if ( !empty( $data['billingAddress'] ) ) {
        $bill = $data['billingAddress'];
        $addr = $bill['address'] ?? [];
        
        $metadata['billing_company'] = $bill['company'] ?? '';
        $metadata['billing_address_1'] = $addr['line1'] ?? '';
        $metadata['billing_address_2'] = $addr['line2'] ?? '';
        $metadata['billing_city'] = $addr['city'] ?? '';
        $metadata['billing_postcode'] = $addr['postcode'] ?? '';
        $metadata['billing_country'] = $addr['countryRegion'] ?? '';
        $metadata['billing_state'] = $addr['stateCounty'] ?? '';
    }
    
    // Add shiptos
    if ( !empty( $data['shiptos'] ) && is_array( $data['shiptos'] ) ) {
        $metadata['shiptos'] = array_map( 'sanitize_text_field', $data['shiptos'] );
    }
    
    // Save all metadata
    foreach ( $metadata as $meta_key => $meta_value ) {
        if ( !empty( $meta_value ) ) {
            $result = add_user_meta( $user_id, $meta_key, $meta_value );
            if ( $result ) {
                error_log( "✅ Added metadata {$meta_key}" );
            } else {
                // Try update if add failed
                update_user_meta( $user_id, $meta_key, $meta_value );
                error_log( "🔄 Updated metadata {$meta_key}" );
            }
        }
    }
    
    // Verify critical metadata
    $saved_customer_id = get_user_meta( $user_id, 'customer_id', true );
    error_log( "🔍 Verification - customer_id: " . $saved_customer_id );

    return rest_ensure_response([
        'success'  => true,
        'user_id'  => $user_id,
        'username' => $username,
        'password' => $password,
        'method'   => 'clean_hooks',
        'metadata_verification' => [
            'customer_id' => $saved_customer_id,
        ]
    ]);
}
?>
