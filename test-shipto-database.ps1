# PowerShell script to test SAP ShipTo API with database integration
# Usage: .\test-shipto-database.ps1

Write-Host "🚀 Testing SAP ShipTo API with Database Integration" -ForegroundColor Green
Write-Host ""

# Configuration - Update these values for your environment
$SiteUrl = "http://localhost/your-wordpress-site"  # Update this URL
$Username = "your_username"                        # Your WordPress username
$Password = "your_password"                        # Your WordPress password (or Application Password)

$ApiEndpoint = "$SiteUrl/wp-json/wc/v3/sap-shipto"
$TestEndpoint = "$SiteUrl/wp-json/wc/v3/sap-shipto-test"

Write-Host "Site URL: $SiteUrl" -ForegroundColor Yellow
Write-Host "API Endpoint: $ApiEndpoint" -ForegroundColor Yellow
Write-Host "Authentication: Using WordPress REST API (Basic Auth)" -ForegroundColor Yellow
Write-Host ""

# Check if credentials are set
if ($Username -eq "your_username" -or $Password -eq "your_password") {
    Write-Host "⚠️  WARNING: Please update the Username and Password variables!" -ForegroundColor Red
    Write-Host "   Use your WordPress admin username and password" -ForegroundColor Yellow
    Write-Host ""
}

# Create Basic Auth credentials
$auth = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${Username}:${Password}"))
$headers = @{
    "Authorization" = "Basic $auth"
    "Content-Type" = "application/json"
}

Write-Host "🔑 Using Basic Authentication with WordPress credentials" -ForegroundColor Cyan
Write-Host ""

# Test 1: Check if the API is active
Write-Host "📡 Test 1: Checking API Status..." -ForegroundColor Cyan

try {
    $testResponse = Invoke-RestMethod -Uri $TestEndpoint -Method Get -ContentType "application/json"
    Write-Host "✅ API Status: " -ForegroundColor Green -NoNewline
    Write-Host $testResponse.message -ForegroundColor White
    Write-Host "   Timestamp: $($testResponse.timestamp)" -ForegroundColor Gray
} catch {
    Write-Host "❌ API Test Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Make sure WordPress is running and the plugin is activated." -ForegroundColor Yellow
    exit 1
}

Write-Host ""

# Test 2: Send sample ShipTo data with multiple addresses
Write-Host "🏢 Test 2: Creating Multiple ShipTo Addresses..." -ForegroundColor Cyan

$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$testCustomerId = "DB_SHIPTO_$timestamp"

$testData = @{
    shipTo = @(
        @{
            addressID = 1128545
            customerId = $testCustomerId
            companyCode = "1000"
            identifier = "Main Warehouse"
            isDefaultAddress = $true
            companyName = "Test Company Inc"
            country = "US"
            address = @{
                street = "123 Main Street"
                houseNumber = "456"
                apartment = "Suite 789"
                city = "Springfield"
                postalCode = "12345"
            }
            stateCounty = "NY"
        },
        @{
            customerId = $testCustomerId
            companyCode = "1000"
            identifier = "Secondary Warehouse"
            isDefaultAddress = $false
            companyName = "Test Company Inc"
            country = "US"
            address = @{
                street = "456 Oak Avenue"
                city = "Springfield"
                postalCode = "12346"
            }
            stateCounty = "NY"
        },
        @{
            customerId = $testCustomerId
            companyCode = "1000"
            identifier = "Distribution Center"
            isDefaultAddress = $false
            companyName = "Test Company Inc"
            country = "CA"
            address = @{
                street = "789 Maple Drive"
                city = "Toronto"
                postalCode = "M5V 3A8"
            }
            stateCounty = "ON"
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host "Sending data:" -ForegroundColor Yellow
Write-Host $testData -ForegroundColor Gray
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $testData -Headers $headers
    
    Write-Host "✅ SUCCESS: ShipTo addresses processed!" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Response Details:" -ForegroundColor Yellow
    Write-Host "  Success: $($response.success)" -ForegroundColor White
    Write-Host "  Total Processed: $($response.total_processed)" -ForegroundColor White
    
    if ($response.processed_customers) {
        foreach ($customer in $response.processed_customers) {
            Write-Host "  Customer: $($customer.customer_id)" -ForegroundColor White
            Write-Host "    Database Updated: $($customer.database_updated)" -ForegroundColor Gray
            Write-Host "    Database Addresses: $($customer.database_addresses_count)" -ForegroundColor Gray
            Write-Host "    WordPress User Found: $($customer.wp_user_found)" -ForegroundColor Gray
            Write-Host "    WordPress User Updated: $($customer.wp_user_updated)" -ForegroundColor Gray
            
            if ($customer.wp_user_id) {
                Write-Host "    WordPress User ID: $($customer.wp_user_id)" -ForegroundColor Gray
            }
            
            if ($customer.wp_user_message) {
                Write-Host "    Message: $($customer.wp_user_message)" -ForegroundColor Gray
            }
        }
    }
    
    if ($response.errors -and $response.errors.Count -gt 0) {
        Write-Host "  Errors:" -ForegroundColor Red
        foreach ($error in $response.errors) {
            Write-Host "    - $error" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "Full Response:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 5 | Write-Host -ForegroundColor Gray
    
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    $errorBody = ""
    
    try {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        $reader.Close()
        $errorStream.Close()
    } catch {
        $errorBody = "Could not read error response"
    }
    
    Write-Host "❌ ERROR: HTTP Status $statusCode" -ForegroundColor Red
    Write-Host "Error Details: $errorBody" -ForegroundColor Red
}

Write-Host ""

# Test 3: Update existing customer addresses
Write-Host "🔄 Test 3: Updating Existing Customer Addresses..." -ForegroundColor Cyan

$updateData = @{
    shipTo = @(
        @{
            addressID = 1128545
            customerId = $testCustomerId
            companyCode = "2000"  # Changed
            identifier = "Updated Main Warehouse"  # Changed
            isDefaultAddress = $true
            companyName = "Updated Test Company Inc"  # Changed
            country = "CA"  # Changed
            address = @{
                street = "999 Updated Street"  # Changed
                city = "Vancouver"  # Changed
                postalCode = "V6B 1A1"  # Changed
            }
            stateCounty = "BC"  # Changed
        },
        @{
            customerId = $testCustomerId
            companyCode = "2000"
            identifier = "New Additional Warehouse"  # New address
            isDefaultAddress = $false
            companyName = "Updated Test Company Inc"
            country = "US"
            address = @{
                street = "111 New Street"
                city = "Seattle"
                postalCode = "98101"
            }
            stateCounty = "WA"
        }
    )
} | ConvertTo-Json -Depth 10

try {
    $updateResponse = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $updateData -Headers $headers
    
    Write-Host "✅ Update successful!" -ForegroundColor Green
    Write-Host "  Total Processed: $($updateResponse.total_processed)" -ForegroundColor White
    
    if ($updateResponse.processed_customers) {
        foreach ($customer in $updateResponse.processed_customers) {
            Write-Host "  Updated Customer: $($customer.customer_id)" -ForegroundColor White
            Write-Host "    Database Addresses: $($customer.database_addresses_count)" -ForegroundColor Gray
        }
    }
    
} catch {
    Write-Host "❌ Update failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 Testing Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "What the API now does:" -ForegroundColor Yellow
Write-Host "1. ✅ Stores addresses in wp_sap_shipto_addresses table in WCMCA format" -ForegroundColor White
Write-Host "2. ✅ Serializes addresses exactly like WordPress metadata" -ForegroundColor White
Write-Host "3. ✅ Supports multiple addresses per customer" -ForegroundColor White
Write-Host "4. ✅ Updates WordPress user metadata if user exists" -ForegroundColor White
Write-Host "5. ✅ Links database records to WordPress users" -ForegroundColor White
Write-Host "6. ✅ Replaces all addresses on each API call" -ForegroundColor White
Write-Host ""
Write-Host "Database Table: wp_sap_shipto_addresses" -ForegroundColor Cyan
Write-Host "Check your database to see the serialized WCMCA data!" -ForegroundColor White
Write-Host ""
Write-Host "WCMCA Format Example:" -ForegroundColor Cyan
Write-Host 'a:2:{i:0;a:9:{s:4:"type";s:8:"shipping";s:10:"address_id";i:1128545;s:21:"address_internal_name";s:14:"Main Warehouse";...}}' -ForegroundColor Gray
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Check the wp_sap_shipto_addresses table in your database" -ForegroundColor White
Write-Host "2. Verify the wcmca_addresses_data field contains serialized PHP array" -ForegroundColor White
Write-Host "3. Check WordPress user metadata was updated (if user exists)" -ForegroundColor White
Write-Host "4. Test copying wcmca_addresses_data to _wcmca_additional_addresses" -ForegroundColor White
