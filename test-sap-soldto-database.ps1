# PowerShell script to test SAP SoldTo API with database integration
# Usage: .\test-sap-soldto-database.ps1

Write-Host "🚀 Testing SAP SoldTo API with Database Integration" -ForegroundColor Green
Write-Host ""

# Configuration - Update these values for your environment
$SiteUrl = "http://localhost/your-wordpress-site"  # Update this URL
$Username = "your_username"                        # Your WordPress username
$Password = "your_password"                        # Your WordPress password (or Application Password)

$ApiEndpoint = "$SiteUrl/wp-json/wc/v3/sap-soldto"
$TestEndpoint = "$SiteUrl/wp-json/wc/v3/sap-soldto-test"

Write-Host "Site URL: $SiteUrl" -ForegroundColor Yellow
Write-Host "API Endpoint: $ApiEndpoint" -ForegroundColor Yellow
Write-Host "Authentication: Using WordPress REST API (Basic Auth)" -ForegroundColor Yellow
Write-Host ""

# Check if credentials are set
if ($Username -eq "your_username" -or $Password -eq "your_password") {
    Write-Host "⚠️  WARNING: Please update the Username and Password variables!" -ForegroundColor Red
    Write-Host "   Use your WordPress admin username and password" -ForegroundColor Yellow
    Write-Host ""
}

# Create Basic Auth credentials
$auth = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${Username}:${Password}"))
$headers = @{
    "Authorization" = "Basic $auth"
    "Content-Type" = "application/json"
}

Write-Host "🔑 Using Basic Authentication with WordPress credentials" -ForegroundColor Cyan
Write-Host ""

# Test 1: Check if the API is active
Write-Host "📡 Test 1: Checking API Status..." -ForegroundColor Cyan

try {
    $testResponse = Invoke-RestMethod -Uri $TestEndpoint -Method Get -ContentType "application/json"
    Write-Host "✅ API Status: " -ForegroundColor Green -NoNewline
    Write-Host $testResponse.message -ForegroundColor White
    Write-Host "   Timestamp: $($testResponse.timestamp)" -ForegroundColor Gray
} catch {
    Write-Host "❌ API Test Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Make sure WordPress is running and the plugin is activated." -ForegroundColor Yellow
    exit 1
}

Write-Host ""

# Test 2: Create new customer (database only)
Write-Host "👤 Test 2: Creating New Customer (Database Only)..." -ForegroundColor Cyan

$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$testCustomerId = "DB_TEST_$timestamp"

$testData = @{
    soldTo = @{
        customerId = $testCustomerId
        companyCode = "1000"
        countryCode = "US"
        priceGroup = "STANDARD"
        email = "dbtest_$<EMAIL>"
    }
    billingAddress = @{
        company = "Database Test Company"
        address = @{
            line1 = "123 Database Street"
            line2 = "Suite 456"
            city = "Test City"
            postcode = "12345"
            countryRegion = "US"
            stateCounty = "NY"
        }
    }
    shiptos = @(
        "WAREHOUSE_DB_001",
        "WAREHOUSE_DB_002"
    )
} | ConvertTo-Json -Depth 10

Write-Host "Sending data:" -ForegroundColor Yellow
Write-Host $testData -ForegroundColor Gray
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $testData -Headers $headers
    
    Write-Host "✅ SUCCESS: Customer data processed!" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Response Details:" -ForegroundColor Yellow
    Write-Host "  Success: $($response.success)" -ForegroundColor White
    Write-Host "  Customer ID: $($response.customer_id)" -ForegroundColor White
    Write-Host "  Database Updated: $($response.database_updated)" -ForegroundColor White
    Write-Host "  Database Record ID: $($response.database_record_id)" -ForegroundColor White
    Write-Host "  WordPress Users Found: $($response.wp_users_found)" -ForegroundColor White
    Write-Host "  WordPress Users Updated: $($response.wp_users_updated)" -ForegroundColor White

    if ($response.wp_user_ids -and $response.wp_user_ids.Count -gt 0) {
        Write-Host "  Updated User IDs: $($response.wp_user_ids -join ', ')" -ForegroundColor White
    }

    if ($response.wp_user_errors -and $response.wp_user_errors.Count -gt 0) {
        Write-Host "  User Update Errors:" -ForegroundColor Red
        foreach ($error in $response.wp_user_errors) {
            Write-Host "    - $error" -ForegroundColor Red
        }
    }
    
    if ($response.message) {
        Write-Host "  Message: $($response.message)" -ForegroundColor Gray
    }
    
    Write-Host ""
    Write-Host "Full Response:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 5 | Write-Host -ForegroundColor Gray
    
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    $errorBody = ""
    
    try {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        $reader.Close()
        $errorStream.Close()
    } catch {
        $errorBody = "Could not read error response"
    }
    
    Write-Host "❌ ERROR: HTTP Status $statusCode" -ForegroundColor Red
    Write-Host "Error Details: $errorBody" -ForegroundColor Red
}

Write-Host ""

# Test 3: Update existing customer
Write-Host "🔄 Test 3: Updating Existing Customer..." -ForegroundColor Cyan

$updateData = @{
    soldTo = @{
        customerId = $testCustomerId
        companyCode = "2000"  # Changed
        countryCode = "CA"    # Changed
        priceGroup = "PREMIUM" # Changed
        email = "updated_$<EMAIL>"
    }
    billingAddress = @{
        company = "Updated Test Company"
        address = @{
            line1 = "456 Updated Street"
            city = "Updated City"
            postcode = "54321"
            countryRegion = "CA"
            stateCounty = "ON"
        }
    }
    shiptos = @(
        "WAREHOUSE_UPDATED_001"
    )
} | ConvertTo-Json -Depth 10

try {
    $updateResponse = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $updateData -Headers $headers
    
    Write-Host "✅ Update successful!" -ForegroundColor Green
    Write-Host "  Database Record ID: $($updateResponse.database_record_id)" -ForegroundColor White
    
} catch {
    Write-Host "❌ Update failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 4: Test with existing WordPress user
Write-Host "👥 Test 4: Testing with Existing WordPress User..." -ForegroundColor Cyan
Write-Host "   Note: This test assumes you have a user with _customer metadata" -ForegroundColor Yellow

$existingUserData = @{
    soldTo = @{
        customerId = "EXISTING_USER_123"  # Change this to match an existing user's _customer value
        companyCode = "3000"
        countryCode = "UK"
        priceGroup = "VIP"
        email = "<EMAIL>"
    }
    billingAddress = @{
        company = "Existing User Company"
        address = @{
            line1 = "789 Existing Street"
            city = "London"
            postcode = "SW1A 1AA"
            countryRegion = "GB"
            stateCounty = "London"
        }
    }
    shiptos = @(
        "WAREHOUSE_EXISTING_001"
    )
} | ConvertTo-Json -Depth 10

try {
    $existingResponse = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $existingUserData -Headers $headers
    
    Write-Host "✅ Existing user test completed!" -ForegroundColor Green
    Write-Host "  WordPress Users Found: $($existingResponse.wp_users_found)" -ForegroundColor White
    Write-Host "  WordPress Users Updated: $($existingResponse.wp_users_updated)" -ForegroundColor White

    if ($existingResponse.wp_user_ids -and $existingResponse.wp_user_ids.Count -gt 0) {
        Write-Host "  Updated User IDs: $($existingResponse.wp_user_ids -join ', ')" -ForegroundColor White
    }
    
} catch {
    Write-Host "⚠️  Existing user test (expected if no user exists): $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Testing Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "What the API now does:" -ForegroundColor Yellow
Write-Host "1. ✅ Always creates/updates record in wp_sap_soldto_customers table" -ForegroundColor White
Write-Host "2. ✅ Searches for existing WordPress user by _customer metadata" -ForegroundColor White
Write-Host "3. ✅ Updates WordPress user metadata if user exists" -ForegroundColor White
Write-Host "4. ✅ Links database record to WordPress user when found" -ForegroundColor White
Write-Host ""
Write-Host "Database Table: wp_sap_soldto_customers" -ForegroundColor Cyan
Write-Host "Check your database to see the created/updated records!" -ForegroundColor White
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Check the wp_sap_soldto_customers table in your database" -ForegroundColor White
Write-Host "2. Verify WordPress user metadata was updated (if user exists)" -ForegroundColor White
Write-Host "3. Check WordPress error logs for detailed processing information" -ForegroundColor White
Write-Host "4. Test with real customer IDs from your system" -ForegroundColor White
