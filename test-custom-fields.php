<?php
/**
 * Test script to verify custom fields are being saved correctly
 */

// Load WordPress
if (!function_exists('get_user_meta')) {
    if (file_exists('./wp-config.php')) {
        require_once('./wp-config.php');
    } elseif (file_exists('../wp-config.php')) {
        require_once('../wp-config.php');
    } else {
        die('WordPress not found.');
    }
}

echo "<h1>Custom Fields Verification Test</h1>\n";

// Get the most recent user (assuming it's the test user)
$users = get_users([
    'orderby' => 'registered',
    'order' => 'DESC',
    'number' => 5
]);

if (empty($users)) {
    echo "<p>❌ No users found</p>\n";
    exit;
}

echo "<h2>Recent Users and Their Custom Fields</h2>\n";

foreach ($users as $user) {
    echo "<h3>User: {$user->user_login} (ID: {$user->ID})</h3>\n";
    echo "<p>Email: {$user->user_email}</p>\n";
    echo "<p>Registered: {$user->user_registered}</p>\n";
    echo "<p>Roles: " . implode(', ', $user->roles) . "</p>\n";
    
    // Check for our specific custom fields
    $custom_fields = [
        'customer_id' => 'Customer ID',
        'company_code' => 'Company Code', 
        'country_code' => 'Country Code',
        'price_group' => 'Price Group'
    ];
    
    echo "<h4>Custom Fields:</h4>\n";
    $has_custom_fields = false;
    
    foreach ($custom_fields as $field_key => $field_name) {
        $value = get_user_meta($user->ID, $field_key, true);
        if (!empty($value)) {
            echo "<p>✅ {$field_name}: <strong>{$value}</strong></p>\n";
            $has_custom_fields = true;
        } else {
            echo "<p>❌ {$field_name}: <em>Not set</em></p>\n";
        }
    }
    
    if (!$has_custom_fields) {
        echo "<p>⚠️ No custom fields found for this user</p>\n";
    }
    
    // Check for billing fields too
    $billing_fields = [
        'billing_company' => 'Billing Company',
        'billing_address_1' => 'Billing Address 1',
        'billing_city' => 'Billing City',
        'billing_country' => 'Billing Country'
    ];
    
    echo "<h4>Billing Fields:</h4>\n";
    foreach ($billing_fields as $field_key => $field_name) {
        $value = get_user_meta($user->ID, $field_key, true);
        if (!empty($value)) {
            echo "<p>✅ {$field_name}: <strong>{$value}</strong></p>\n";
        }
    }
    
    // Check shiptos
    $shiptos = get_user_meta($user->ID, 'shiptos', true);
    if (!empty($shiptos)) {
        echo "<h4>Ship-To Locations:</h4>\n";
        if (is_array($shiptos)) {
            foreach ($shiptos as $shipto) {
                echo "<p>📦 {$shipto}</p>\n";
            }
        } else {
            echo "<p>📦 {$shiptos}</p>\n";
        }
    }
    
    echo "<hr>\n";
}

echo "<h2>Test Summary</h2>\n";
echo "<p>This script shows the custom fields for the most recent users.</p>\n";
echo "<p>Look for users with the following fields populated:</p>\n";
echo "<ul>\n";
echo "<li>✅ Customer ID (customer_id)</li>\n";
echo "<li>✅ Company Code (company_code)</li>\n";
echo "<li>✅ Country Code (country_code)</li>\n";
echo "<li>✅ Price Group (price_group)</li>\n";
echo "<li>✅ Role: b2b_customer</li>\n";
echo "</ul>\n";
?>
