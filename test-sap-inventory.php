<?php
/**
 * Test script for SAP Inventory API
 * This script tests the plugin functionality and shows current product stock levels
 */

// Load WordPress
if (!function_exists('get_post_meta')) {
    if (file_exists('./wp-config.php')) {
        require_once('./wp-config.php');
    } elseif (file_exists('../wp-config.php')) {
        require_once('../wp-config.php');
    } else {
        die('WordPress not found.');
    }
}

echo "<h1>SAP Inventory API Test</h1>\n";

// Test data matching your example
$test_data = [
    "inventory" => [
        "materialNumber" => "M12345",
        "stock" => [
            "US" => [
                "quantity" => 100,
                "allowBackorders" => false
            ],
            "EU" => [
                "quantity" => 200,
                "allowBackorders" => true
            ]
        ]
    ]
];

echo "<h2>Test Data</h2>\n";
echo "<pre>" . json_encode($test_data, JSON_PRETTY_PRINT) . "</pre>\n";

// Check if we have a test product with materialNumber M12345
echo "<h2>Checking for Test Product</h2>\n";

$test_product = get_posts([
    'post_type' => 'product',
    'post_status' => 'publish',
    'title' => 'M12345',
    'numberposts' => 1
]);

if (empty($test_product)) {
    echo "<p>❌ No product found with title 'M12345'</p>\n";
    echo "<p>Creating a test product...</p>\n";
    
    // Create test product
    $product_data = [
        'post_title' => 'M12345',
        'post_content' => 'Test product for SAP Inventory API',
        'post_status' => 'publish',
        'post_type' => 'product'
    ];
    
    $product_id = wp_insert_post($product_data);
    
    if (is_wp_error($product_id)) {
        echo "<p>❌ Failed to create test product: " . $product_id->get_error_message() . "</p>\n";
        exit;
    }
    
    // Set basic WooCommerce product meta
    update_post_meta($product_id, '_visibility', 'visible');
    update_post_meta($product_id, '_stock_status', 'instock');
    update_post_meta($product_id, '_manage_stock', 'yes');
    update_post_meta($product_id, '_sku', 'M12345');
    
    echo "<p>✅ Created test product with ID: {$product_id}</p>\n";
} else {
    $product_id = $test_product[0]->ID;
    echo "<p>✅ Found existing test product with ID: {$product_id}</p>\n";
}

// Show current stock levels before processing
echo "<h2>Current Stock Levels (Before)</h2>\n";
$current_stock = get_product_stock_levels($product_id);
if (empty($current_stock)) {
    echo "<p>No regional stock data found</p>\n";
} else {
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Meta Key</th><th>Value</th></tr>\n";
    foreach ($current_stock as $key => $value) {
        echo "<tr><td>{$key}</td><td>{$value}</td></tr>\n";
    }
    echo "</table>\n";
}

// Test the processing functions
echo "<h2>Testing Inventory Processing</h2>\n";

if (function_exists('find_product_by_material_number')) {
    echo "<h3>Testing Product Lookup</h3>\n";
    $found_product_id = find_product_by_material_number('M12345');
    if ($found_product_id) {
        echo "<p>✅ Product lookup successful: Found product ID {$found_product_id}</p>\n";
    } else {
        echo "<p>❌ Product lookup failed</p>\n";
    }
} else {
    echo "<p>❌ Plugin functions not loaded. Make sure the plugin is activated.</p>\n";
}

if (function_exists('update_product_stock_for_region')) {
    echo "<h3>Testing Stock Updates</h3>\n";
    
    foreach ($test_data['inventory']['stock'] as $region => $stock_data) {
        echo "<h4>Updating {$region} Stock</h4>\n";
        
        $result = update_product_stock_for_region($product_id, $region, $stock_data);
        
        if (is_wp_error($result)) {
            echo "<p>❌ Error: " . $result->get_error_message() . "</p>\n";
        } else {
            echo "<p>✅ Success: " . print_r($result, true) . "</p>\n";
        }
    }
} else {
    echo "<p>❌ Plugin stock update functions not loaded.</p>\n";
}

// Show final stock levels after processing
echo "<h2>Final Stock Levels (After)</h2>\n";
$final_stock = get_product_stock_levels($product_id);
if (empty($final_stock)) {
    echo "<p>No regional stock data found</p>\n";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Meta Key</th><th>Value</th><th>Description</th></tr>\n";
    foreach ($final_stock as $key => $value) {
        $description = '';
        if (strpos($key, '_stock_') === 0) {
            $region = str_replace('_stock_', '', $key);
            $description = "Stock quantity for " . strtoupper($region);
        } elseif (strpos($key, '_backorders_') === 0) {
            $region = str_replace('_backorders_', '', $key);
            $description = "Backorders setting for " . strtoupper($region) . " (yes/no)";
        }
        echo "<tr><td><strong>{$key}</strong></td><td>{$value}</td><td>{$description}</td></tr>\n";
    }
    echo "</table>\n";
}

// Show expected field mapping
echo "<h2>Field Mapping Reference</h2>\n";
echo "<p>The SAP Inventory API creates these custom fields:</p>\n";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr><th>Region</th><th>Stock Field</th><th>Backorders Field</th><th>Description</th></tr>\n";

$regions = ['US', 'EU', 'CA', 'UK', 'AU'];
foreach ($regions as $region) {
    $region_lower = strtolower($region);
    echo "<tr>";
    echo "<td><strong>{$region}</strong></td>";
    echo "<td>_stock_{$region_lower}</td>";
    echo "<td>_backorders_{$region_lower}</td>";
    echo "<td>Stock and backorder settings for {$region}</td>";
    echo "</tr>\n";
}
echo "</table>\n";

// Test the REST API endpoint
echo "<h2>Testing REST API Endpoint</h2>\n";
echo "<p>You can test the REST API endpoint by sending a POST request to:</p>\n";
echo "<code>" . home_url('/wp-json/wc/v3/sap-inventory') . "</code>\n";
echo "<p>With the following JSON data:</p>\n";
echo "<pre>" . json_encode($test_data, JSON_PRETTY_PRINT) . "</pre>\n";

echo "<h2>cURL Command for Testing</h2>\n";
echo "<pre>";
echo "curl -X POST \\\n";
echo "  '" . home_url('/wp-json/wc/v3/sap-inventory') . "' \\\n";
echo "  -u 'username:password' \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($test_data) . "'";
echo "</pre>\n";

echo "<h2>Postman Collection</h2>\n";
echo "<p>For Postman testing:</p>\n";
echo "<ul>\n";
echo "<li><strong>Method:</strong> POST</li>\n";
echo "<li><strong>URL:</strong> " . home_url('/wp-json/wc/v3/sap-inventory') . "</li>\n";
echo "<li><strong>Authorization:</strong> Basic Auth (WordPress username/password)</li>\n";
echo "<li><strong>Headers:</strong> Content-Type: application/json</li>\n";
echo "<li><strong>Body:</strong> Raw JSON (see above)</li>\n";
echo "</ul>\n";

echo "<h2>Product Management</h2>\n";
echo "<p>To create more test products:</p>\n";
echo "<ol>\n";
echo "<li>Go to <strong>WooCommerce → Products → Add New</strong></li>\n";
echo "<li>Set the <strong>Product Title</strong> to your material number (e.g., 'M67890')</li>\n";
echo "<li>Set the <strong>SKU</strong> to the same material number</li>\n";
echo "<li>Enable <strong>Manage Stock</strong></li>\n";
echo "<li>Publish the product</li>\n";
echo "</ol>\n";

echo "<h2>Test Complete</h2>\n";
echo "<p>Check the WordPress error logs for detailed processing information.</p>\n";

/**
 * Helper function to get current stock levels for a product
 */
function get_product_stock_levels($product_id) {
    $stock_levels = [];
    
    // Get all meta for the product
    $all_meta = get_post_meta($product_id);
    
    foreach ($all_meta as $key => $value) {
        if (strpos($key, '_stock_') === 0 || strpos($key, '_backorders_') === 0) {
            $stock_levels[$key] = is_array($value) ? $value[0] : $value;
        }
    }
    
    return $stock_levels;
}
?>
