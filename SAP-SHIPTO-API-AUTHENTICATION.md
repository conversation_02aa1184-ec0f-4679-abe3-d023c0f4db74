# SAP ShipTo API Authentication Setup

The SAP ShipTo Addresses API uses WordPress REST API authentication for security.

## 🔐 Authentication Methods

The API supports **WordPress REST API authentication** which provides:
- Built-in WordPress security
- Multiple authentication options
- Easy to set up and manage
- No additional plugins required

## 🚀 Authentication Options

### Option 1: Basic Authentication (Recommended)

Use your WordPress username and password with Basic Auth:

```bash
# Test the API with Basic Auth
curl -X GET \
  'https://yoursite.com/wp-json/wc/v3/sap-shipto-test' \
  -u 'your_username:your_password'
```

### Option 2: Application Passwords (WordPress 5.6+)

Create an application password for enhanced security:

1. **Go to WordPress Admin** → **Users** → **Your Profile**
2. **Scroll down to "Application Passwords"**
3. **Enter application name**: `SAP ShipTo Integration`
4. **Click "Add New Application Password"**
5. **Copy the generated password** (format: `xxxx xxxx xxxx xxxx xxxx xxxx`)

```bash
# Test with Application Password
curl -X GET \
  'https://yoursite.com/wp-json/wc/v3/sap-shipto-test' \
  -u 'your_username:xxxx xxxx xxxx xxxx xxxx xxxx'
```

### Option 3: Cookie Authentication (For same-domain requests)

If calling from the same domain (e.g., from WordPress admin):

```javascript
// JavaScript example with nonce
fetch('/wp-json/wc/v3/sap-shipto-test', {
    method: 'GET',
    credentials: 'same-origin',
    headers: {
        'X-WP-Nonce': wpApiSettings.nonce
    }
});
```

## 📡 Using the Authenticated API

### JWT Bearer Token Authentication

```bash
curl -X POST \
  'https://yoursite.com/wp-json/wc/v3/sap-shipto' \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "shipTo": [
      {
        "customerId": "C123456",
        "identifier": "Main Warehouse",
        "isDefaultAddress": true,
        "country": "US",
        "address": {
          "street": "123 Main St",
          "city": "Springfield",
          "postalCode": "12345"
        }
      }
    ]
  }'
```

### Complete Workflow Example

```bash
# Step 1: Get JWT token
TOKEN_RESPONSE=$(curl -s -X POST \
  'https://yoursite.com/wp-json/jwt-auth/v1/token' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }')

# Step 2: Extract token from response
TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.token')

# Step 3: Use token to call SAP ShipTo API
curl -X POST \
  'https://yoursite.com/wp-json/wc/v3/sap-shipto' \
  -H "Authorization: Bearer $TOKEN" \
  -H 'Content-Type: application/json' \
  -d '{"shipTo": [...]}'
```

## 🔧 PowerShell Example with JWT Authentication

```powershell
# Set your credentials
$Username = "your_username"
$Password = "your_password"
$SiteUrl = "https://yoursite.com"

# Step 1: Get JWT token
$tokenData = @{
    username = $Username
    password = $Password
} | ConvertTo-Json

try {
    $tokenResponse = Invoke-RestMethod -Uri "$SiteUrl/wp-json/jwt-auth/v1/token" -Method Post -Body $tokenData -ContentType "application/json"
    $jwtToken = $tokenResponse.token
    Write-Host "✅ JWT token obtained successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to get JWT token: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Create headers with JWT token
$headers = @{
    "Authorization" = "Bearer $jwtToken"
    "Content-Type" = "application/json"
}

# Step 3: Your shipping data
$shipToData = @{
    shipTo = @(
        @{
            customerId = "C123456"
            identifier = "Main Warehouse"
            isDefaultAddress = $true
            country = "US"
            address = @{
                street = "123 Main St"
                city = "Springfield"
                postalCode = "12345"
            }
        }
    )
} | ConvertTo-Json -Depth 10

# Step 4: Send the request
try {
    $response = Invoke-RestMethod -Uri "$SiteUrl/wp-json/wc/v3/sap-shipto" -Method Post -Headers $headers -Body $shipToData
    Write-Host "✅ Success!" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}
```

## 🛡️ Security Best Practices

### 1. **API Key Management**
- Use descriptive names for API keys
- Regularly rotate API keys
- Delete unused API keys
- Monitor API key usage

### 2. **Permissions**
- Only grant `Read/Write` permissions (not `Read/Write/Delete`)
- Create dedicated API keys for each integration
- Don't share API keys between systems

### 3. **Network Security**
- Always use HTTPS in production
- Consider IP whitelisting if possible
- Monitor API access logs

### 4. **Error Handling**
- Don't expose API keys in error messages
- Log authentication failures for monitoring
- Implement rate limiting if needed

## 🔍 Troubleshooting Authentication

### Common Issues:

1. **401 Unauthorized**
   - Check consumer key/secret are correct
   - Verify the API key exists and is active
   - Ensure you're using the right authentication method

2. **403 Forbidden**
   - API key doesn't have sufficient permissions
   - User associated with API key lacks capabilities
   - Check the API key permissions are `Read/Write`

3. **404 Not Found**
   - Plugin not activated
   - Wrong endpoint URL
   - WooCommerce not active

### Debug Steps:

1. **Test the test endpoint first**:
   ```bash
   curl -u 'key:secret' 'https://yoursite.com/wp-json/wc/v3/sap-shipto-test'
   ```

2. **Check WordPress error logs** for authentication messages

3. **Verify WooCommerce API keys** in admin panel

4. **Test with a simple WooCommerce endpoint**:
   ```bash
   curl -u 'key:secret' 'https://yoursite.com/wp-json/wc/v3/products'
   ```

## 📝 API Response Codes

- **200**: Success - addresses processed
- **400**: Bad request - invalid data format
- **401**: Unauthorized - authentication failed
- **403**: Forbidden - insufficient permissions
- **404**: Not found - endpoint not available
- **500**: Server error - check logs

## 🔄 Migration from Test Version

If you were using the test version without authentication:

1. **Create API keys** as described above
2. **Update your integration** to include authentication
3. **Test thoroughly** before going live
4. **Remove any test/debug plugins** that bypass authentication

The API functionality remains exactly the same - only authentication is now required for security.
