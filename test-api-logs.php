<?php
/**
 * Test script to verify API logging functionality
 * This script checks if the APIlogs.log file is being created and written to
 */

// Load WordPress
if (!function_exists('wp_mkdir_p')) {
    if (file_exists('./wp-config.php')) {
        require_once('./wp-config.php');
    } elseif (file_exists('../wp-config.php')) {
        require_once('../wp-config.php');
    } else {
        die('WordPress not found.');
    }
}

echo "<h1>SAP API Logging Test</h1>\n";

$log_dir = WP_CONTENT_DIR . '/logs';
$log_file = $log_dir . '/APIlogs.log';

echo "<h2>Log File Information</h2>\n";
echo "<p><strong>Log Directory:</strong> {$log_dir}</p>\n";
echo "<p><strong>Log File:</strong> {$log_file}</p>\n";

// Check if logs directory exists
if (file_exists($log_dir)) {
    echo "<p>✅ Logs directory exists</p>\n";
    
    // Check directory permissions
    if (is_writable($log_dir)) {
        echo "<p>✅ Logs directory is writable</p>\n";
    } else {
        echo "<p>❌ Logs directory is not writable</p>\n";
    }
} else {
    echo "<p>⚠️ Logs directory does not exist - will be created on first API call</p>\n";
}

// Check if log file exists
if (file_exists($log_file)) {
    echo "<p>✅ APIlogs.log file exists</p>\n";
    
    // Get file size
    $file_size = filesize($log_file);
    echo "<p><strong>File Size:</strong> " . number_format($file_size) . " bytes</p>\n";
    
    // Get last modified time
    $last_modified = filemtime($log_file);
    echo "<p><strong>Last Modified:</strong> " . date('Y-m-d H:i:s', $last_modified) . "</p>\n";
    
    // Show last 20 lines of the log file
    echo "<h3>Last 20 Log Entries</h3>\n";
    $lines = file($log_file);
    $last_lines = array_slice($lines, -20);
    
    echo "<div style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto;'>";
    foreach ($last_lines as $line) {
        echo htmlspecialchars($line);
    }
    echo "</div>\n";
    
} else {
    echo "<p>⚠️ APIlogs.log file does not exist yet - will be created on first API call</p>\n";
}

// Test the logging function
echo "<h2>Testing Log Function</h2>\n";

// Test if the SAP logging functions exist
$functions_to_test = [
    'sap_log_api' => 'SAP SoldTo',
    'sap_inventory_log_api' => 'SAP Inventory', 
    'sap_shipto_log_api' => 'SAP ShipTo'
];

foreach ($functions_to_test as $function_name => $plugin_name) {
    if (function_exists($function_name)) {
        echo "<p>✅ {$plugin_name} logging function exists: <code>{$function_name}()</code></p>\n";
        
        // Test the function
        try {
            $test_message = "TEST LOG ENTRY from {$plugin_name} - " . current_time('mysql');
            call_user_func($function_name, $test_message);
            echo "<p>✅ Successfully wrote test log entry for {$plugin_name}</p>\n";
        } catch (Exception $e) {
            echo "<p>❌ Error testing {$plugin_name} logging: " . $e->getMessage() . "</p>\n";
        }
    } else {
        echo "<p>❌ {$plugin_name} logging function not found: <code>{$function_name}()</code></p>\n";
        echo "<p>   Make sure the {$plugin_name} plugin is activated</p>\n";
    }
}

// Manual test of the logging mechanism
echo "<h2>Manual Logging Test</h2>\n";

try {
    // Create logs directory if it doesn't exist
    if (!file_exists($log_dir)) {
        wp_mkdir_p($log_dir);
        echo "<p>✅ Created logs directory</p>\n";
    }
    
    // Write a test entry
    $timestamp = current_time('Y-m-d H:i:s');
    $test_entry = "[{$timestamp}] 🧪 MANUAL TEST LOG ENTRY - API Logging Test Script" . PHP_EOL;
    
    $result = file_put_contents($log_file, $test_entry, FILE_APPEND | LOCK_EX);
    
    if ($result !== false) {
        echo "<p>✅ Successfully wrote manual test entry ({$result} bytes)</p>\n";
    } else {
        echo "<p>❌ Failed to write manual test entry</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error in manual logging test: " . $e->getMessage() . "</p>\n";
}

// Show updated log file content if it exists
if (file_exists($log_file)) {
    echo "<h3>Updated Log File Content (Last 10 Lines)</h3>\n";
    $lines = file($log_file);
    $last_lines = array_slice($lines, -10);
    
    echo "<div style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc; font-family: monospace; white-space: pre-wrap;'>";
    foreach ($last_lines as $line) {
        echo htmlspecialchars($line);
    }
    echo "</div>\n";
}

echo "<h2>API Endpoints to Test</h2>\n";
echo "<p>To generate real API logs, test these endpoints:</p>\n";
echo "<ul>\n";
echo "<li><strong>SAP SoldTo:</strong> POST " . home_url('/wp-json/wc/v3/sap-soldto') . "</li>\n";
echo "<li><strong>SAP Inventory:</strong> POST " . home_url('/wp-json/wc/v3/sap-inventory') . "</li>\n";
echo "<li><strong>SAP ShipTo:</strong> POST " . home_url('/wp-json/wc/v3/sap-shipto') . "</li>\n";
echo "</ul>\n";

echo "<h2>Log File Location</h2>\n";
echo "<p>Your API logs are stored at:</p>\n";
echo "<code>{$log_file}</code>\n";
echo "<p>You can monitor this file in real-time using:</p>\n";
echo "<code>tail -f {$log_file}</code>\n";

echo "<h2>Log Rotation Recommendation</h2>\n";
echo "<p>⚠️ <strong>Important:</strong> The APIlogs.log file will grow over time. Consider setting up log rotation to prevent it from becoming too large.</p>\n";
echo "<p>You can set up a cron job to rotate the logs weekly or monthly.</p>\n";

echo "<h2>Test Complete</h2>\n";
echo "<p>✅ API logging test completed. Check the log file to verify entries are being written.</p>\n";
?>
